(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function br(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const he={},Yt=[],ct=()=>{},ql=()=>!1,ds=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),vr=e=>e.startsWith("onUpdate:"),Fe=Object.assign,xr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},zl=Object.prototype.hasOwnProperty,fe=(e,t)=>zl.call(e,t),X=Array.isArray,Qt=e=>Ln(e)==="[object Map]",ps=e=>Ln(e)==="[object Set]",qr=e=>Ln(e)==="[object Date]",te=e=>typeof e=="function",we=e=>typeof e=="string",Xe=e=>typeof e=="symbol",be=e=>e!==null&&typeof e=="object",Yo=e=>(be(e)||te(e))&&te(e.then)&&te(e.catch),Qo=Object.prototype.toString,Ln=e=>Qo.call(e),Kl=e=>Ln(e).slice(8,-1),ei=e=>Ln(e)==="[object Object]",wr=e=>we(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,vn=br(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),hs=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Wl=/-(\w)/g,Je=hs(e=>e.replace(Wl,(t,n)=>n?n.toUpperCase():"")),Jl=/\B([A-Z])/g,Bt=hs(e=>e.replace(Jl,"-$1").toLowerCase()),ms=hs(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ls=hs(e=>e?`on${ms(e)}`:""),Pt=(e,t)=>!Object.is(e,t),Kn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ti=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},es=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let zr;const gs=()=>zr||(zr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ys(e){if(X(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=we(s)?Yl(s):ys(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(we(e)||be(e))return e}const Gl=/;(?![^(]*\))/g,Zl=/:([^]+)/,Xl=/\/\*[^]*?\*\//g;function Yl(e){const t={};return e.replace(Xl,"").split(Gl).forEach(n=>{if(n){const s=n.split(Zl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Te(e){let t="";if(we(e))t=e;else if(X(e))for(let n=0;n<e.length;n++){const s=Te(e[n]);s&&(t+=s+" ")}else if(be(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ql="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ea=br(Ql);function ni(e){return!!e||e===""}function ta(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=bs(e[s],t[s]);return n}function bs(e,t){if(e===t)return!0;let n=qr(e),s=qr(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Xe(e),s=Xe(t),n||s)return e===t;if(n=X(e),s=X(t),n||s)return n&&s?ta(e,t):!1;if(n=be(e),s=be(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!bs(e[i],t[i]))return!1}}return String(e)===String(t)}function na(e,t){return e.findIndex(n=>bs(n,t))}const si=e=>!!(e&&e.__v_isRef===!0),ae=e=>we(e)?e:e==null?"":X(e)||be(e)&&(e.toString===Qo||!te(e.toString))?si(e)?ae(e.value):JSON.stringify(e,ri,2):String(e),ri=(e,t)=>si(t)?ri(e,t.value):Qt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Ns(s,o)+" =>"]=r,n),{})}:ps(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ns(n))}:Xe(t)?Ns(t):be(t)&&!X(t)&&!ei(t)?String(t):t,Ns=(e,t="")=>{var n;return Xe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ne;class sa{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ne,!t&&Ne&&(this.index=(Ne.scopes||(Ne.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ne;try{return Ne=this,t()}finally{Ne=n}}}on(){++this._on===1&&(this.prevScope=Ne,Ne=this)}off(){this._on>0&&--this._on===0&&(Ne=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ra(){return Ne}let ye;const Hs=new WeakSet;class oi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ne&&Ne.active&&Ne.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Hs.has(this)&&(Hs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||li(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Kr(this),ai(this);const t=ye,n=Ge;ye=this,Ge=!0;try{return this.fn()}finally{ci(this),ye=t,Ge=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Sr(t);this.deps=this.depsTail=void 0,Kr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Hs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Qs(this)&&this.run()}get dirty(){return Qs(this)}}let ii=0,xn,wn;function li(e,t=!1){if(e.flags|=8,t){e.next=wn,wn=e;return}e.next=xn,xn=e}function _r(){ii++}function Cr(){if(--ii>0)return;if(wn){let t=wn;for(wn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;xn;){let t=xn;for(xn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function ai(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ci(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Sr(s),oa(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Qs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ui(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ui(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===An)||(e.globalVersion=An,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Qs(e))))return;e.flags|=2;const t=e.dep,n=ye,s=Ge;ye=e,Ge=!0;try{ai(e);const r=e.fn(e._value);(t.version===0||Pt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ye=n,Ge=s,ci(e),e.flags&=-3}}function Sr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Sr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function oa(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ge=!0;const fi=[];function bt(){fi.push(Ge),Ge=!1}function vt(){const e=fi.pop();Ge=e===void 0?!0:e}function Kr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ye;ye=void 0;try{t()}finally{ye=n}}}let An=0;class ia{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Rr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ye||!Ge||ye===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ye)n=this.activeLink=new ia(ye,this),ye.deps?(n.prevDep=ye.depsTail,ye.depsTail.nextDep=n,ye.depsTail=n):ye.deps=ye.depsTail=n,di(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ye.depsTail,n.nextDep=void 0,ye.depsTail.nextDep=n,ye.depsTail=n,ye.deps===n&&(ye.deps=s)}return n}trigger(t){this.version++,An++,this.notify(t)}notify(t){_r();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Cr()}}}function di(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)di(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const er=new WeakMap,Lt=Symbol(""),tr=Symbol(""),Tn=Symbol("");function Re(e,t,n){if(Ge&&ye){let s=er.get(e);s||er.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Rr),r.map=s,r.key=n),r.track()}}function gt(e,t,n,s,r,o){const i=er.get(e);if(!i){An++;return}const l=a=>{a&&a.trigger()};if(_r(),t==="clear")i.forEach(l);else{const a=X(e),u=a&&wr(n);if(a&&n==="length"){const c=Number(s);i.forEach((f,h)=>{(h==="length"||h===Tn||!Xe(h)&&h>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(Tn)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Lt)),Qt(e)&&l(i.get(tr)));break;case"delete":a||(l(i.get(Lt)),Qt(e)&&l(i.get(tr)));break;case"set":Qt(e)&&l(i.get(Lt));break}}Cr()}function Wt(e){const t=ue(e);return t===e?t:(Re(t,"iterate",Tn),Ke(e)?t:t.map(Ce))}function vs(e){return Re(e=ue(e),"iterate",Tn),e}const la={__proto__:null,[Symbol.iterator](){return Us(this,Symbol.iterator,Ce)},concat(...e){return Wt(this).concat(...e.map(t=>X(t)?Wt(t):t))},entries(){return Us(this,"entries",e=>(e[1]=Ce(e[1]),e))},every(e,t){return dt(this,"every",e,t,void 0,arguments)},filter(e,t){return dt(this,"filter",e,t,n=>n.map(Ce),arguments)},find(e,t){return dt(this,"find",e,t,Ce,arguments)},findIndex(e,t){return dt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return dt(this,"findLast",e,t,Ce,arguments)},findLastIndex(e,t){return dt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return dt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ds(this,"includes",e)},indexOf(...e){return Ds(this,"indexOf",e)},join(e){return Wt(this).join(e)},lastIndexOf(...e){return Ds(this,"lastIndexOf",e)},map(e,t){return dt(this,"map",e,t,void 0,arguments)},pop(){return pn(this,"pop")},push(...e){return pn(this,"push",e)},reduce(e,...t){return Wr(this,"reduce",e,t)},reduceRight(e,...t){return Wr(this,"reduceRight",e,t)},shift(){return pn(this,"shift")},some(e,t){return dt(this,"some",e,t,void 0,arguments)},splice(...e){return pn(this,"splice",e)},toReversed(){return Wt(this).toReversed()},toSorted(e){return Wt(this).toSorted(e)},toSpliced(...e){return Wt(this).toSpliced(...e)},unshift(...e){return pn(this,"unshift",e)},values(){return Us(this,"values",Ce)}};function Us(e,t,n){const s=vs(e),r=s[t]();return s!==e&&!Ke(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const aa=Array.prototype;function dt(e,t,n,s,r,o){const i=vs(e),l=i!==e&&!Ke(e),a=i[t];if(a!==aa[t]){const f=a.apply(e,o);return l?Ce(f):f}let u=n;i!==e&&(l?u=function(f,h){return n.call(this,Ce(f),h,e)}:n.length>2&&(u=function(f,h){return n.call(this,f,h,e)}));const c=a.call(i,u,s);return l&&r?r(c):c}function Wr(e,t,n,s){const r=vs(e);let o=n;return r!==e&&(Ke(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,Ce(l),a,e)}),r[t](o,...s)}function Ds(e,t,n){const s=ue(e);Re(s,"iterate",Tn);const r=s[t](...n);return(r===-1||r===!1)&&Tr(n[0])?(n[0]=ue(n[0]),s[t](...n)):r}function pn(e,t,n=[]){bt(),_r();const s=ue(e)[t].apply(e,n);return Cr(),vt(),s}const ca=br("__proto__,__v_isRef,__isVue"),pi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Xe));function ua(e){Xe(e)||(e=String(e));const t=ue(this);return Re(t,"has",e),t.hasOwnProperty(e)}class hi{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?xa:bi:o?yi:gi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=X(t);if(!r){let a;if(i&&(a=la[n]))return a;if(n==="hasOwnProperty")return ua}const l=Reflect.get(t,n,Pe(t)?t:s);return(Xe(n)?pi.has(n):ca(n))||(r||Re(t,"get",n),o)?l:Pe(l)?i&&wr(n)?l:l.value:be(l)?r?xi(l):Nn(l):l}}class mi extends hi{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const a=Ot(o);if(!Ke(s)&&!Ot(s)&&(o=ue(o),s=ue(s)),!X(t)&&Pe(o)&&!Pe(s))return a?!1:(o.value=s,!0)}const i=X(t)&&wr(n)?Number(n)<t.length:fe(t,n),l=Reflect.set(t,n,s,Pe(t)?t:r);return t===ue(r)&&(i?Pt(s,o)&&gt(t,"set",n,s):gt(t,"add",n,s)),l}deleteProperty(t,n){const s=fe(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&gt(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Xe(n)||!pi.has(n))&&Re(t,"has",n),s}ownKeys(t){return Re(t,"iterate",X(t)?"length":Lt),Reflect.ownKeys(t)}}class fa extends hi{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const da=new mi,pa=new fa,ha=new mi(!0);const nr=e=>e,Vn=e=>Reflect.getPrototypeOf(e);function ma(e,t,n){return function(...s){const r=this.__v_raw,o=ue(r),i=Qt(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=r[e](...s),c=n?nr:t?ts:Ce;return!t&&Re(o,"iterate",a?tr:Lt),{next(){const{value:f,done:h}=u.next();return h?{value:f,done:h}:{value:l?[c(f[0]),c(f[1])]:c(f),done:h}},[Symbol.iterator](){return this}}}}function qn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ga(e,t){const n={get(r){const o=this.__v_raw,i=ue(o),l=ue(r);e||(Pt(r,l)&&Re(i,"get",r),Re(i,"get",l));const{has:a}=Vn(i),u=t?nr:e?ts:Ce;if(a.call(i,r))return u(o.get(r));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&Re(ue(r),"iterate",Lt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ue(o),l=ue(r);return e||(Pt(r,l)&&Re(i,"has",r),Re(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=ue(l),u=t?nr:e?ts:Ce;return!e&&Re(a,"iterate",Lt),l.forEach((c,f)=>r.call(o,u(c),u(f),i))}};return Fe(n,e?{add:qn("add"),set:qn("set"),delete:qn("delete"),clear:qn("clear")}:{add(r){!t&&!Ke(r)&&!Ot(r)&&(r=ue(r));const o=ue(this);return Vn(o).has.call(o,r)||(o.add(r),gt(o,"add",r,r)),this},set(r,o){!t&&!Ke(o)&&!Ot(o)&&(o=ue(o));const i=ue(this),{has:l,get:a}=Vn(i);let u=l.call(i,r);u||(r=ue(r),u=l.call(i,r));const c=a.call(i,r);return i.set(r,o),u?Pt(o,c)&&gt(i,"set",r,o):gt(i,"add",r,o),this},delete(r){const o=ue(this),{has:i,get:l}=Vn(o);let a=i.call(o,r);a||(r=ue(r),a=i.call(o,r)),l&&l.call(o,r);const u=o.delete(r);return a&&gt(o,"delete",r,void 0),u},clear(){const r=ue(this),o=r.size!==0,i=r.clear();return o&&gt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=ma(r,e,t)}),n}function Er(e,t){const n=ga(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(fe(n,r)&&r in s?n:s,r,o)}const ya={get:Er(!1,!1)},ba={get:Er(!1,!0)},va={get:Er(!0,!1)};const gi=new WeakMap,yi=new WeakMap,bi=new WeakMap,xa=new WeakMap;function wa(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function _a(e){return e.__v_skip||!Object.isExtensible(e)?0:wa(Kl(e))}function Nn(e){return Ot(e)?e:Ar(e,!1,da,ya,gi)}function vi(e){return Ar(e,!1,ha,ba,yi)}function xi(e){return Ar(e,!0,pa,va,bi)}function Ar(e,t,n,s,r){if(!be(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=_a(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function en(e){return Ot(e)?en(e.__v_raw):!!(e&&e.__v_isReactive)}function Ot(e){return!!(e&&e.__v_isReadonly)}function Ke(e){return!!(e&&e.__v_isShallow)}function Tr(e){return e?!!e.__v_raw:!1}function ue(e){const t=e&&e.__v_raw;return t?ue(t):e}function Ca(e){return!fe(e,"__v_skip")&&Object.isExtensible(e)&&ti(e,"__v_skip",!0),e}const Ce=e=>be(e)?Nn(e):e,ts=e=>be(e)?xi(e):e;function Pe(e){return e?e.__v_isRef===!0:!1}function Z(e){return wi(e,!1)}function Sa(e){return wi(e,!0)}function wi(e,t){return Pe(e)?e:new Ra(e,t)}class Ra{constructor(t,n){this.dep=new Rr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ue(t),this._value=n?t:Ce(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ke(t)||Ot(t);t=s?t:ue(t),Pt(t,n)&&(this._rawValue=t,this._value=s?t:Ce(t),this.dep.trigger())}}function Ve(e){return Pe(e)?e.value:e}const Ea={get:(e,t,n)=>t==="__v_raw"?e:Ve(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Pe(r)&&!Pe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function _i(e){return en(e)?e:new Proxy(e,Ea)}class Aa{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Rr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=An-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ye!==this)return li(this,!0),!0}get value(){const t=this.dep.track();return ui(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ta(e,t,n=!1){let s,r;return te(e)?s=e:(s=e.get,r=e.set),new Aa(s,r,n)}const zn={},ns=new WeakMap;let $t;function Pa(e,t=!1,n=$t){if(n){let s=ns.get(n);s||ns.set(n,s=[]),s.push(e)}}function Oa(e,t,n=he){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=n,u=L=>r?L:Ke(L)||r===!1||r===0?yt(L,1):yt(L);let c,f,h,p,y=!1,w=!1;if(Pe(e)?(f=()=>e.value,y=Ke(e)):en(e)?(f=()=>u(e),y=!0):X(e)?(w=!0,y=e.some(L=>en(L)||Ke(L)),f=()=>e.map(L=>{if(Pe(L))return L.value;if(en(L))return u(L);if(te(L))return a?a(L,2):L()})):te(e)?t?f=a?()=>a(e,2):e:f=()=>{if(h){bt();try{h()}finally{vt()}}const L=$t;$t=c;try{return a?a(e,3,[p]):e(p)}finally{$t=L}}:f=ct,t&&r){const L=f,Y=r===!0?1/0:r;f=()=>yt(L(),Y)}const _=ra(),T=()=>{c.stop(),_&&_.active&&xr(_.effects,c)};if(o&&t){const L=t;t=(...Y)=>{L(...Y),T()}}let O=w?new Array(e.length).fill(zn):zn;const I=L=>{if(!(!(c.flags&1)||!c.dirty&&!L))if(t){const Y=c.run();if(r||y||(w?Y.some((le,oe)=>Pt(le,O[oe])):Pt(Y,O))){h&&h();const le=$t;$t=c;try{const oe=[Y,O===zn?void 0:w&&O[0]===zn?[]:O,p];O=Y,a?a(t,3,oe):t(...oe)}finally{$t=le}}}else c.run()};return l&&l(I),c=new oi(f),c.scheduler=i?()=>i(I,!1):I,p=L=>Pa(L,!1,c),h=c.onStop=()=>{const L=ns.get(c);if(L){if(a)a(L,4);else for(const Y of L)Y();ns.delete(c)}},t?s?I(!0):O=c.run():i?i(I.bind(null,!0),!0):c.run(),T.pause=c.pause.bind(c),T.resume=c.resume.bind(c),T.stop=T,T}function yt(e,t=1/0,n){if(t<=0||!be(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Pe(e))yt(e.value,t,n);else if(X(e))for(let s=0;s<e.length;s++)yt(e[s],t,n);else if(ps(e)||Qt(e))e.forEach(s=>{yt(s,t,n)});else if(ei(e)){for(const s in e)yt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&yt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hn(e,t,n,s){try{return s?e(...s):e()}catch(r){xs(r,t,n)}}function ut(e,t,n,s){if(te(e)){const r=Hn(e,t,n,s);return r&&Yo(r)&&r.catch(o=>{xs(o,t,n)}),r}if(X(e)){const r=[];for(let o=0;o<e.length;o++)r.push(ut(e[o],t,n,s));return r}}function xs(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||he;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(o){bt(),Hn(o,null,10,[e,a,u]),vt();return}}Fa(e,n,r,s,i)}function Fa(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const $e=[];let lt=-1;const tn=[];let Rt=null,Zt=0;const Ci=Promise.resolve();let ss=null;function Vt(e){const t=ss||Ci;return e?t.then(this?e.bind(this):e):t}function ka(e){let t=lt+1,n=$e.length;for(;t<n;){const s=t+n>>>1,r=$e[s],o=Pn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Pr(e){if(!(e.flags&1)){const t=Pn(e),n=$e[$e.length-1];!n||!(e.flags&2)&&t>=Pn(n)?$e.push(e):$e.splice(ka(t),0,e),e.flags|=1,Si()}}function Si(){ss||(ss=Ci.then(Ei))}function $a(e){X(e)?tn.push(...e):Rt&&e.id===-1?Rt.splice(Zt+1,0,e):e.flags&1||(tn.push(e),e.flags|=1),Si()}function Jr(e,t,n=lt+1){for(;n<$e.length;n++){const s=$e[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;$e.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Ri(e){if(tn.length){const t=[...new Set(tn)].sort((n,s)=>Pn(n)-Pn(s));if(tn.length=0,Rt){Rt.push(...t);return}for(Rt=t,Zt=0;Zt<Rt.length;Zt++){const n=Rt[Zt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Rt=null,Zt=0}}const Pn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ei(e){try{for(lt=0;lt<$e.length;lt++){const t=$e[lt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Hn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;lt<$e.length;lt++){const t=$e[lt];t&&(t.flags&=-2)}lt=-1,$e.length=0,Ri(),ss=null,($e.length||tn.length)&&Ei()}}let Se=null,Ai=null;function rs(e){const t=Se;return Se=e,Ai=e&&e.type.__scopeId||null,t}function Ti(e,t=Se,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&ro(-1);const o=rs(t);let i;try{i=e(...r)}finally{rs(o),s._d&&ro(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Nt(e,t){if(Se===null)return e;const n=Ss(Se),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=he]=t[r];o&&(te(o)&&(o={mounted:o,updated:o}),o.deep&&yt(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Ft(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[s];a&&(bt(),ut(a,n,8,[e.el,l,e,t]),vt())}}const Ma=Symbol("_vte"),Ia=e=>e.__isTeleport;function Or(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Or(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Pi(e,t){return te(e)?Fe({name:e.name},t,{setup:e}):e}function Oi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function os(e,t,n,s,r=!1){if(X(e)){e.forEach((y,w)=>os(y,t&&(X(t)?t[w]:t),n,s,r));return}if(nn(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&os(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Ss(s.component):s.el,i=r?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===he?l.refs={}:l.refs,f=l.setupState,h=ue(f),p=f===he?()=>!1:y=>fe(h,y);if(u!=null&&u!==a&&(we(u)?(c[u]=null,p(u)&&(f[u]=null)):Pe(u)&&(u.value=null)),te(a))Hn(a,l,12,[i,c]);else{const y=we(a),w=Pe(a);if(y||w){const _=()=>{if(e.f){const T=y?p(a)?f[a]:c[a]:a.value;r?X(T)&&xr(T,o):X(T)?T.includes(o)||T.push(o):y?(c[a]=[o],p(a)&&(f[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else y?(c[a]=i,p(a)&&(f[a]=i)):w&&(a.value=i,e.k&&(c[e.k]=i))};i?(_.id=-1,Be(_,n)):_()}}}gs().requestIdleCallback;gs().cancelIdleCallback;const nn=e=>!!e.type.__asyncLoader,Fi=e=>e.type.__isKeepAlive;function La(e,t){ki(e,"a",t)}function Na(e,t){ki(e,"da",t)}function ki(e,t,n=Ee){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(ws(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Fi(r.parent.vnode)&&Ha(s,t,n,r),r=r.parent}}function Ha(e,t,n,s){const r=ws(t,e,s,!0);Fr(()=>{xr(s[t],r)},n)}function ws(e,t,n=Ee,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{bt();const l=Un(n),a=ut(t,n,e,i);return l(),vt(),a});return s?r.unshift(o):r.push(o),o}}const wt=e=>(t,n=Ee)=>{(!kn||e==="sp")&&ws(e,(...s)=>t(...s),n)},Ua=wt("bm"),qt=wt("m"),Da=wt("bu"),ja=wt("u"),Ba=wt("bum"),Fr=wt("um"),Va=wt("sp"),qa=wt("rtg"),za=wt("rtc");function Ka(e,t=Ee){ws("ec",e,t)}const $i="components";function Wa(e,t){return Ii($i,e,!0,t)||e}const Mi=Symbol.for("v-ndc");function Ja(e){return we(e)?Ii($i,e,!1)||e:e||Mi}function Ii(e,t,n=!0,s=!1){const r=Se||Ee;if(r){const o=r.type;{const l=Ic(o,!1);if(l&&(l===t||l===Je(t)||l===ms(Je(t))))return o}const i=Gr(r[e]||o[e],t)||Gr(r.appContext[e],t);return!i&&s?o:i}}function Gr(e,t){return e&&(e[t]||e[Je(t)]||e[ms(Je(t))])}function Ye(e,t,n,s){let r;const o=n,i=X(e);if(i||we(e)){const l=i&&en(e);let a=!1,u=!1;l&&(a=!Ke(e),u=Ot(e),e=vs(e)),r=new Array(e.length);for(let c=0,f=e.length;c<f;c++)r[c]=t(a?u?ts(Ce(e[c])):Ce(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(be(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];r[a]=t(e[c],c,a,o)}}else r=[];return r}function Ga(e,t,n={},s,r){if(Se.ce||Se.parent&&nn(Se.parent)&&Se.parent.ce)return V(),Ut(xe,null,[me("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),V();const i=o&&Li(o(n)),l=n.key||i&&i.key,a=Ut(xe,{key:(l&&!Xe(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),o&&o._c&&(o._d=!0),a}function Li(e){return e.some(t=>Fn(t)?!(t.type===xt||t.type===xe&&!Li(t.children)):!0)?e:null}const sr=e=>e?nl(e)?Ss(e):sr(e.parent):null,_n=Fe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>sr(e.parent),$root:e=>sr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Hi(e),$forceUpdate:e=>e.f||(e.f=()=>{Pr(e.update)}),$nextTick:e=>e.n||(e.n=Vt.bind(e.proxy)),$watch:e=>gc.bind(e)}),js=(e,t)=>e!==he&&!e.__isScriptSetup&&fe(e,t),Za={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const p=i[t];if(p!==void 0)switch(p){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(js(s,t))return i[t]=1,s[t];if(r!==he&&fe(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&fe(u,t))return i[t]=3,o[t];if(n!==he&&fe(n,t))return i[t]=4,n[t];rr&&(i[t]=0)}}const c=_n[t];let f,h;if(c)return t==="$attrs"&&Re(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==he&&fe(n,t))return i[t]=4,n[t];if(h=a.config.globalProperties,fe(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return js(r,t)?(r[t]=n,!0):s!==he&&fe(s,t)?(s[t]=n,!0):fe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==he&&fe(e,i)||js(t,i)||(l=o[0])&&fe(l,i)||fe(s,i)||fe(_n,i)||fe(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:fe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Zr(e){return X(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let rr=!0;function Xa(e){const t=Hi(e),n=e.proxy,s=e.ctx;rr=!1,t.beforeCreate&&Xr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:h,beforeUpdate:p,updated:y,activated:w,deactivated:_,beforeDestroy:T,beforeUnmount:O,destroyed:I,unmounted:L,render:Y,renderTracked:le,renderTriggered:oe,errorCaptured:q,serverPrefetch:v,expose:C,inheritAttrs:k,components:F,directives:D,filters:B}=t;if(u&&Ya(u,s,null),i)for(const ee in i){const re=i[ee];te(re)&&(s[ee]=re.bind(n))}if(r){const ee=r.call(n,n);be(ee)&&(e.data=Nn(ee))}if(rr=!0,o)for(const ee in o){const re=o[ee],ft=te(re)?re.bind(n,n):te(re.get)?re.get.bind(n,n):ct,_t=!te(re)&&te(re.set)?re.set.bind(n):ct,tt=ve({get:ft,set:_t});Object.defineProperty(s,ee,{enumerable:!0,configurable:!0,get:()=>tt.value,set:Ie=>tt.value=Ie})}if(l)for(const ee in l)Ni(l[ee],s,n,ee);if(a){const ee=te(a)?a.call(n):a;Reflect.ownKeys(ee).forEach(re=>{Wn(re,ee[re])})}c&&Xr(c,e,"c");function se(ee,re){X(re)?re.forEach(ft=>ee(ft.bind(n))):re&&ee(re.bind(n))}if(se(Ua,f),se(qt,h),se(Da,p),se(ja,y),se(La,w),se(Na,_),se(Ka,q),se(za,le),se(qa,oe),se(Ba,O),se(Fr,L),se(Va,v),X(C))if(C.length){const ee=e.exposed||(e.exposed={});C.forEach(re=>{Object.defineProperty(ee,re,{get:()=>n[re],set:ft=>n[re]=ft})})}else e.exposed||(e.exposed={});Y&&e.render===ct&&(e.render=Y),k!=null&&(e.inheritAttrs=k),F&&(e.components=F),D&&(e.directives=D),v&&Oi(e)}function Ya(e,t,n=ct){X(e)&&(e=or(e));for(const s in e){const r=e[s];let o;be(r)?"default"in r?o=Ze(r.from||s,r.default,!0):o=Ze(r.from||s):o=Ze(r),Pe(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Xr(e,t,n){ut(X(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ni(e,t,n,s){let r=s.includes(".")?Xi(n,s):()=>n[s];if(we(e)){const o=t[e];te(o)&&Cn(r,o)}else if(te(e))Cn(r,e.bind(n));else if(be(e))if(X(e))e.forEach(o=>Ni(o,t,n,s));else{const o=te(e.handler)?e.handler.bind(n):t[e.handler];te(o)&&Cn(r,o,e)}}function Hi(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!n&&!s?a=t:(a={},r.length&&r.forEach(u=>is(a,u,i,!0)),is(a,t,i)),be(t)&&o.set(t,a),a}function is(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&is(e,o,n,!0),r&&r.forEach(i=>is(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Qa[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Qa={data:Yr,props:Qr,emits:Qr,methods:bn,computed:bn,beforeCreate:ke,created:ke,beforeMount:ke,mounted:ke,beforeUpdate:ke,updated:ke,beforeDestroy:ke,beforeUnmount:ke,destroyed:ke,unmounted:ke,activated:ke,deactivated:ke,errorCaptured:ke,serverPrefetch:ke,components:bn,directives:bn,watch:tc,provide:Yr,inject:ec};function Yr(e,t){return t?e?function(){return Fe(te(e)?e.call(this,this):e,te(t)?t.call(this,this):t)}:t:e}function ec(e,t){return bn(or(e),or(t))}function or(e){if(X(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ke(e,t){return e?[...new Set([].concat(e,t))]:t}function bn(e,t){return e?Fe(Object.create(null),e,t):t}function Qr(e,t){return e?X(e)&&X(t)?[...new Set([...e,...t])]:Fe(Object.create(null),Zr(e),Zr(t??{})):t}function tc(e,t){if(!e)return t;if(!t)return e;const n=Fe(Object.create(null),e);for(const s in t)n[s]=ke(e[s],t[s]);return n}function Ui(){return{app:null,config:{isNativeTag:ql,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let nc=0;function sc(e,t){return function(s,r=null){te(s)||(s=Fe({},s)),r!=null&&!be(r)&&(r=null);const o=Ui(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:nc++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Nc,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&te(c.install)?(i.add(c),c.install(u,...f)):te(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,h){if(!a){const p=u._ceVNode||me(s,r);return p.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),e(p,c,h),a=!0,u._container=c,c.__vue_app__=u,Ss(p.component)}},onUnmount(c){l.push(c)},unmount(){a&&(ut(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=sn;sn=u;try{return c()}finally{sn=f}}};return u}}let sn=null;function Wn(e,t){if(Ee){let n=Ee.provides;const s=Ee.parent&&Ee.parent.provides;s===n&&(n=Ee.provides=Object.create(s)),n[e]=t}}function Ze(e,t,n=!1){const s=Ee||Se;if(s||sn){let r=sn?sn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&te(t)?t.call(s&&s.proxy):t}}const Di={},ji=()=>Object.create(Di),Bi=e=>Object.getPrototypeOf(e)===Di;function rc(e,t,n,s=!1){const r={},o=ji();e.propsDefaults=Object.create(null),Vi(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:vi(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function oc(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ue(r),[a]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let h=c[f];if(_s(e.emitsOptions,h))continue;const p=t[h];if(a)if(fe(o,h))p!==o[h]&&(o[h]=p,u=!0);else{const y=Je(h);r[y]=ir(a,l,y,p,e,!1)}else p!==o[h]&&(o[h]=p,u=!0)}}}else{Vi(e,t,r,o)&&(u=!0);let c;for(const f in l)(!t||!fe(t,f)&&((c=Bt(f))===f||!fe(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(r[f]=ir(a,l,f,void 0,e,!0)):delete r[f]);if(o!==l)for(const f in o)(!t||!fe(t,f))&&(delete o[f],u=!0)}u&&gt(e.attrs,"set","")}function Vi(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(vn(a))continue;const u=t[a];let c;r&&fe(r,c=Je(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:_s(e.emitsOptions,a)||(!(a in s)||u!==s[a])&&(s[a]=u,i=!0)}if(o){const a=ue(n),u=l||he;for(let c=0;c<o.length;c++){const f=o[c];n[f]=ir(r,a,f,u[f],e,!fe(u,f))}}return i}function ir(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=fe(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&te(a)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const c=Un(r);s=u[n]=a.call(null,t),c()}}else s=a;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===Bt(n))&&(s=!0))}return s}const ic=new WeakMap;function qi(e,t,n=!1){const s=n?ic:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!te(e)){const c=f=>{a=!0;const[h,p]=qi(f,t,!0);Fe(i,h),p&&l.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return be(e)&&s.set(e,Yt),Yt;if(X(o))for(let c=0;c<o.length;c++){const f=Je(o[c]);eo(f)&&(i[f]=he)}else if(o)for(const c in o){const f=Je(c);if(eo(f)){const h=o[c],p=i[f]=X(h)||te(h)?{type:h}:Fe({},h),y=p.type;let w=!1,_=!0;if(X(y))for(let T=0;T<y.length;++T){const O=y[T],I=te(O)&&O.name;if(I==="Boolean"){w=!0;break}else I==="String"&&(_=!1)}else w=te(y)&&y.name==="Boolean";p[0]=w,p[1]=_,(w||fe(p,"default"))&&l.push(f)}}const u=[i,l];return be(e)&&s.set(e,u),u}function eo(e){return e[0]!=="$"&&!vn(e)}const kr=e=>e[0]==="_"||e==="$stable",$r=e=>X(e)?e.map(at):[at(e)],lc=(e,t,n)=>{if(t._n)return t;const s=Ti((...r)=>$r(t(...r)),n);return s._c=!1,s},zi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(kr(r))continue;const o=e[r];if(te(o))t[r]=lc(r,o,s);else if(o!=null){const i=$r(o);t[r]=()=>i}}},Ki=(e,t)=>{const n=$r(t);e.slots.default=()=>n},Wi=(e,t,n)=>{for(const s in t)(n||!kr(s))&&(e[s]=t[s])},ac=(e,t,n)=>{const s=e.slots=ji();if(e.vnode.shapeFlag&32){const r=t._;r?(Wi(s,t,n),n&&ti(s,"_",r,!0)):zi(t,s)}else t&&Ki(e,t)},cc=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=he;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Wi(r,t,n):(o=!t.$stable,zi(t,r)),i=t}else t&&(Ki(e,t),i={default:1});if(o)for(const l in r)!kr(l)&&i[l]==null&&delete r[l]},Be=Cc;function uc(e){return fc(e)}function fc(e,t){const n=gs();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:h,setScopeId:p=ct,insertStaticContent:y}=e,w=(d,m,b,S=null,A=null,E=null,N=void 0,M=null,$=!!m.dynamicChildren)=>{if(d===m)return;d&&!hn(d,m)&&(S=R(d),Ie(d,A,E,!0),d=null),m.patchFlag===-2&&($=!1,m.dynamicChildren=null);const{type:P,ref:G,shapeFlag:U}=m;switch(P){case Cs:_(d,m,b,S);break;case xt:T(d,m,b,S);break;case Jn:d==null&&O(m,b,S,N);break;case xe:F(d,m,b,S,A,E,N,M,$);break;default:U&1?Y(d,m,b,S,A,E,N,M,$):U&6?D(d,m,b,S,A,E,N,M,$):(U&64||U&128)&&P.process(d,m,b,S,A,E,N,M,$,z)}G!=null&&A&&os(G,d&&d.ref,E,m||d,!m)},_=(d,m,b,S)=>{if(d==null)s(m.el=l(m.children),b,S);else{const A=m.el=d.el;m.children!==d.children&&u(A,m.children)}},T=(d,m,b,S)=>{d==null?s(m.el=a(m.children||""),b,S):m.el=d.el},O=(d,m,b,S)=>{[d.el,d.anchor]=y(d.children,m,b,S,d.el,d.anchor)},I=({el:d,anchor:m},b,S)=>{let A;for(;d&&d!==m;)A=h(d),s(d,b,S),d=A;s(m,b,S)},L=({el:d,anchor:m})=>{let b;for(;d&&d!==m;)b=h(d),r(d),d=b;r(m)},Y=(d,m,b,S,A,E,N,M,$)=>{m.type==="svg"?N="svg":m.type==="math"&&(N="mathml"),d==null?le(m,b,S,A,E,N,M,$):v(d,m,A,E,N,M,$)},le=(d,m,b,S,A,E,N,M)=>{let $,P;const{props:G,shapeFlag:U,transition:W,dirs:Q}=d;if($=d.el=i(d.type,E,G&&G.is,G),U&8?c($,d.children):U&16&&q(d.children,$,null,S,A,Bs(d,E),N,M),Q&&Ft(d,null,S,"created"),oe($,d,d.scopeId,N,S),G){for(const ge in G)ge!=="value"&&!vn(ge)&&o($,ge,null,G[ge],E,S);"value"in G&&o($,"value",null,G.value,E),(P=G.onVnodeBeforeMount)&&ot(P,S,d)}Q&&Ft(d,null,S,"beforeMount");const ie=dc(A,W);ie&&W.beforeEnter($),s($,m,b),((P=G&&G.onVnodeMounted)||ie||Q)&&Be(()=>{P&&ot(P,S,d),ie&&W.enter($),Q&&Ft(d,null,S,"mounted")},A)},oe=(d,m,b,S,A)=>{if(b&&p(d,b),S)for(let E=0;E<S.length;E++)p(d,S[E]);if(A){let E=A.subTree;if(m===E||Qi(E.type)&&(E.ssContent===m||E.ssFallback===m)){const N=A.vnode;oe(d,N,N.scopeId,N.slotScopeIds,A.parent)}}},q=(d,m,b,S,A,E,N,M,$=0)=>{for(let P=$;P<d.length;P++){const G=d[P]=M?Et(d[P]):at(d[P]);w(null,G,m,b,S,A,E,N,M)}},v=(d,m,b,S,A,E,N)=>{const M=m.el=d.el;let{patchFlag:$,dynamicChildren:P,dirs:G}=m;$|=d.patchFlag&16;const U=d.props||he,W=m.props||he;let Q;if(b&&kt(b,!1),(Q=W.onVnodeBeforeUpdate)&&ot(Q,b,m,d),G&&Ft(m,d,b,"beforeUpdate"),b&&kt(b,!0),(U.innerHTML&&W.innerHTML==null||U.textContent&&W.textContent==null)&&c(M,""),P?C(d.dynamicChildren,P,M,b,S,Bs(m,A),E):N||re(d,m,M,null,b,S,Bs(m,A),E,!1),$>0){if($&16)k(M,U,W,b,A);else if($&2&&U.class!==W.class&&o(M,"class",null,W.class,A),$&4&&o(M,"style",U.style,W.style,A),$&8){const ie=m.dynamicProps;for(let ge=0;ge<ie.length;ge++){const de=ie[ge],De=U[de],Le=W[de];(Le!==De||de==="value")&&o(M,de,De,Le,A,b)}}$&1&&d.children!==m.children&&c(M,m.children)}else!N&&P==null&&k(M,U,W,b,A);((Q=W.onVnodeUpdated)||G)&&Be(()=>{Q&&ot(Q,b,m,d),G&&Ft(m,d,b,"updated")},S)},C=(d,m,b,S,A,E,N)=>{for(let M=0;M<m.length;M++){const $=d[M],P=m[M],G=$.el&&($.type===xe||!hn($,P)||$.shapeFlag&198)?f($.el):b;w($,P,G,null,S,A,E,N,!0)}},k=(d,m,b,S,A)=>{if(m!==b){if(m!==he)for(const E in m)!vn(E)&&!(E in b)&&o(d,E,m[E],null,A,S);for(const E in b){if(vn(E))continue;const N=b[E],M=m[E];N!==M&&E!=="value"&&o(d,E,M,N,A,S)}"value"in b&&o(d,"value",m.value,b.value,A)}},F=(d,m,b,S,A,E,N,M,$)=>{const P=m.el=d?d.el:l(""),G=m.anchor=d?d.anchor:l("");let{patchFlag:U,dynamicChildren:W,slotScopeIds:Q}=m;Q&&(M=M?M.concat(Q):Q),d==null?(s(P,b,S),s(G,b,S),q(m.children||[],b,G,A,E,N,M,$)):U>0&&U&64&&W&&d.dynamicChildren?(C(d.dynamicChildren,W,b,A,E,N,M),(m.key!=null||A&&m===A.subTree)&&Ji(d,m,!0)):re(d,m,b,G,A,E,N,M,$)},D=(d,m,b,S,A,E,N,M,$)=>{m.slotScopeIds=M,d==null?m.shapeFlag&512?A.ctx.activate(m,b,S,N,$):B(m,b,S,A,E,N,$):K(d,m,$)},B=(d,m,b,S,A,E,N)=>{const M=d.component=Oc(d,S,A);if(Fi(d)&&(M.ctx.renderer=z),Fc(M,!1,N),M.asyncDep){if(A&&A.registerDep(M,se,N),!d.el){const $=M.subTree=me(xt);T(null,$,m,b)}}else se(M,d,m,b,A,E,N)},K=(d,m,b)=>{const S=m.component=d.component;if(wc(d,m,b))if(S.asyncDep&&!S.asyncResolved){ee(S,m,b);return}else S.next=m,S.update();else m.el=d.el,S.vnode=m},se=(d,m,b,S,A,E,N)=>{const M=()=>{if(d.isMounted){let{next:U,bu:W,u:Q,parent:ie,vnode:ge}=d;{const st=Gi(d);if(st){U&&(U.el=ge.el,ee(d,U,N)),st.asyncDep.then(()=>{d.isUnmounted||M()});return}}let de=U,De;kt(d,!1),U?(U.el=ge.el,ee(d,U,N)):U=ge,W&&Kn(W),(De=U.props&&U.props.onVnodeBeforeUpdate)&&ot(De,ie,U,ge),kt(d,!0);const Le=no(d),nt=d.subTree;d.subTree=Le,w(nt,Le,f(nt.el),R(nt),d,A,E),U.el=Le.el,de===null&&_c(d,Le.el),Q&&Be(Q,A),(De=U.props&&U.props.onVnodeUpdated)&&Be(()=>ot(De,ie,U,ge),A)}else{let U;const{el:W,props:Q}=m,{bm:ie,m:ge,parent:de,root:De,type:Le}=d,nt=nn(m);kt(d,!1),ie&&Kn(ie),!nt&&(U=Q&&Q.onVnodeBeforeMount)&&ot(U,de,m),kt(d,!0);{De.ce&&De.ce._injectChildStyle(Le);const st=d.subTree=no(d);w(null,st,b,S,d,A,E),m.el=st.el}if(ge&&Be(ge,A),!nt&&(U=Q&&Q.onVnodeMounted)){const st=m;Be(()=>ot(U,de,st),A)}(m.shapeFlag&256||de&&nn(de.vnode)&&de.vnode.shapeFlag&256)&&d.a&&Be(d.a,A),d.isMounted=!0,m=b=S=null}};d.scope.on();const $=d.effect=new oi(M);d.scope.off();const P=d.update=$.run.bind($),G=d.job=$.runIfDirty.bind($);G.i=d,G.id=d.uid,$.scheduler=()=>Pr(G),kt(d,!0),P()},ee=(d,m,b)=>{m.component=d;const S=d.vnode.props;d.vnode=m,d.next=null,oc(d,m.props,S,b),cc(d,m.children,b),bt(),Jr(d),vt()},re=(d,m,b,S,A,E,N,M,$=!1)=>{const P=d&&d.children,G=d?d.shapeFlag:0,U=m.children,{patchFlag:W,shapeFlag:Q}=m;if(W>0){if(W&128){_t(P,U,b,S,A,E,N,M,$);return}else if(W&256){ft(P,U,b,S,A,E,N,M,$);return}}Q&8?(G&16&&ze(P,A,E),U!==P&&c(b,U)):G&16?Q&16?_t(P,U,b,S,A,E,N,M,$):ze(P,A,E,!0):(G&8&&c(b,""),Q&16&&q(U,b,S,A,E,N,M,$))},ft=(d,m,b,S,A,E,N,M,$)=>{d=d||Yt,m=m||Yt;const P=d.length,G=m.length,U=Math.min(P,G);let W;for(W=0;W<U;W++){const Q=m[W]=$?Et(m[W]):at(m[W]);w(d[W],Q,b,null,A,E,N,M,$)}P>G?ze(d,A,E,!0,!1,U):q(m,b,S,A,E,N,M,$,U)},_t=(d,m,b,S,A,E,N,M,$)=>{let P=0;const G=m.length;let U=d.length-1,W=G-1;for(;P<=U&&P<=W;){const Q=d[P],ie=m[P]=$?Et(m[P]):at(m[P]);if(hn(Q,ie))w(Q,ie,b,null,A,E,N,M,$);else break;P++}for(;P<=U&&P<=W;){const Q=d[U],ie=m[W]=$?Et(m[W]):at(m[W]);if(hn(Q,ie))w(Q,ie,b,null,A,E,N,M,$);else break;U--,W--}if(P>U){if(P<=W){const Q=W+1,ie=Q<G?m[Q].el:S;for(;P<=W;)w(null,m[P]=$?Et(m[P]):at(m[P]),b,ie,A,E,N,M,$),P++}}else if(P>W)for(;P<=U;)Ie(d[P],A,E,!0),P++;else{const Q=P,ie=P,ge=new Map;for(P=ie;P<=W;P++){const je=m[P]=$?Et(m[P]):at(m[P]);je.key!=null&&ge.set(je.key,P)}let de,De=0;const Le=W-ie+1;let nt=!1,st=0;const dn=new Array(Le);for(P=0;P<Le;P++)dn[P]=0;for(P=Q;P<=U;P++){const je=d[P];if(De>=Le){Ie(je,A,E,!0);continue}let rt;if(je.key!=null)rt=ge.get(je.key);else for(de=ie;de<=W;de++)if(dn[de-ie]===0&&hn(je,m[de])){rt=de;break}rt===void 0?Ie(je,A,E,!0):(dn[rt-ie]=P+1,rt>=st?st=rt:nt=!0,w(je,m[rt],b,null,A,E,N,M,$),De++)}const Br=nt?pc(dn):Yt;for(de=Br.length-1,P=Le-1;P>=0;P--){const je=ie+P,rt=m[je],Vr=je+1<G?m[je+1].el:S;dn[P]===0?w(null,rt,b,Vr,A,E,N,M,$):nt&&(de<0||P!==Br[de]?tt(rt,b,Vr,2):de--)}}},tt=(d,m,b,S,A=null)=>{const{el:E,type:N,transition:M,children:$,shapeFlag:P}=d;if(P&6){tt(d.component.subTree,m,b,S);return}if(P&128){d.suspense.move(m,b,S);return}if(P&64){N.move(d,m,b,z);return}if(N===xe){s(E,m,b);for(let U=0;U<$.length;U++)tt($[U],m,b,S);s(d.anchor,m,b);return}if(N===Jn){I(d,m,b);return}if(S!==2&&P&1&&M)if(S===0)M.beforeEnter(E),s(E,m,b),Be(()=>M.enter(E),A);else{const{leave:U,delayLeave:W,afterLeave:Q}=M,ie=()=>{d.ctx.isUnmounted?r(E):s(E,m,b)},ge=()=>{U(E,()=>{ie(),Q&&Q()})};W?W(E,ie,ge):ge()}else s(E,m,b)},Ie=(d,m,b,S=!1,A=!1)=>{const{type:E,props:N,ref:M,children:$,dynamicChildren:P,shapeFlag:G,patchFlag:U,dirs:W,cacheIndex:Q}=d;if(U===-2&&(A=!1),M!=null&&(bt(),os(M,null,b,d,!0),vt()),Q!=null&&(m.renderCache[Q]=void 0),G&256){m.ctx.deactivate(d);return}const ie=G&1&&W,ge=!nn(d);let de;if(ge&&(de=N&&N.onVnodeBeforeUnmount)&&ot(de,m,d),G&6)Bn(d.component,b,S);else{if(G&128){d.suspense.unmount(b,S);return}ie&&Ft(d,null,m,"beforeUnmount"),G&64?d.type.remove(d,m,b,z,S):P&&!P.hasOnce&&(E!==xe||U>0&&U&64)?ze(P,m,b,!1,!0):(E===xe&&U&384||!A&&G&16)&&ze($,m,b),S&&zt(d)}(ge&&(de=N&&N.onVnodeUnmounted)||ie)&&Be(()=>{de&&ot(de,m,d),ie&&Ft(d,null,m,"unmounted")},b)},zt=d=>{const{type:m,el:b,anchor:S,transition:A}=d;if(m===xe){Kt(b,S);return}if(m===Jn){L(d);return}const E=()=>{r(b),A&&!A.persisted&&A.afterLeave&&A.afterLeave()};if(d.shapeFlag&1&&A&&!A.persisted){const{leave:N,delayLeave:M}=A,$=()=>N(b,E);M?M(d.el,E,$):$()}else E()},Kt=(d,m)=>{let b;for(;d!==m;)b=h(d),r(d),d=b;r(m)},Bn=(d,m,b)=>{const{bum:S,scope:A,job:E,subTree:N,um:M,m:$,a:P,parent:G,slots:{__:U}}=d;to($),to(P),S&&Kn(S),G&&X(U)&&U.forEach(W=>{G.renderCache[W]=void 0}),A.stop(),E&&(E.flags|=8,Ie(N,d,m,b)),M&&Be(M,m),Be(()=>{d.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},ze=(d,m,b,S=!1,A=!1,E=0)=>{for(let N=E;N<d.length;N++)Ie(d[N],m,b,S,A)},R=d=>{if(d.shapeFlag&6)return R(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const m=h(d.anchor||d.el),b=m&&m[Ma];return b?h(b):m};let j=!1;const H=(d,m,b)=>{d==null?m._vnode&&Ie(m._vnode,null,null,!0):w(m._vnode||null,d,m,null,null,null,b),m._vnode=d,j||(j=!0,Jr(),Ri(),j=!1)},z={p:w,um:Ie,m:tt,r:zt,mt:B,mc:q,pc:re,pbc:C,n:R,o:e};return{render:H,hydrate:void 0,createApp:sc(H)}}function Bs({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function kt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function dc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ji(e,t,n=!1){const s=e.children,r=t.children;if(X(s)&&X(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=Et(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Ji(i,l)),l.type===Cs&&(l.el=i.el),l.type===xt&&!l.el&&(l.el=i.el)}}function pc(e){const t=e.slice(),n=[0];let s,r,o,i,l;const a=e.length;for(s=0;s<a;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Gi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Gi(t)}function to(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const hc=Symbol.for("v-scx"),mc=()=>Ze(hc);function Cn(e,t,n){return Zi(e,t,n)}function Zi(e,t,n=he){const{immediate:s,deep:r,flush:o,once:i}=n,l=Fe({},n),a=t&&s||!t&&o!=="post";let u;if(kn){if(o==="sync"){const p=mc();u=p.__watcherHandles||(p.__watcherHandles=[])}else if(!a){const p=()=>{};return p.stop=ct,p.resume=ct,p.pause=ct,p}}const c=Ee;l.call=(p,y,w)=>ut(p,c,y,w);let f=!1;o==="post"?l.scheduler=p=>{Be(p,c&&c.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(p,y)=>{y?p():Pr(p)}),l.augmentJob=p=>{t&&(p.flags|=4),f&&(p.flags|=2,c&&(p.id=c.uid,p.i=c))};const h=Oa(e,t,l);return kn&&(u?u.push(h):a&&h()),h}function gc(e,t,n){const s=this.proxy,r=we(e)?e.includes(".")?Xi(s,e):()=>s[e]:e.bind(s,s);let o;te(t)?o=t:(o=t.handler,n=t);const i=Un(this),l=Zi(r,o.bind(s),n);return i(),l}function Xi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const yc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Je(t)}Modifiers`]||e[`${Bt(t)}Modifiers`];function bc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||he;let r=n;const o=t.startsWith("update:"),i=o&&yc(s,t.slice(7));i&&(i.trim&&(r=n.map(c=>we(c)?c.trim():c)),i.number&&(r=n.map(es)));let l,a=s[l=Ls(t)]||s[l=Ls(Je(t))];!a&&o&&(a=s[l=Ls(Bt(t))]),a&&ut(a,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ut(u,e,6,r)}}function Yi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!te(e)){const a=u=>{const c=Yi(u,t,!0);c&&(l=!0,Fe(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(be(e)&&s.set(e,null),null):(X(o)?o.forEach(a=>i[a]=null):Fe(i,o),be(e)&&s.set(e,i),i)}function _s(e,t){return!e||!ds(t)?!1:(t=t.slice(2).replace(/Once$/,""),fe(e,t[0].toLowerCase()+t.slice(1))||fe(e,Bt(t))||fe(e,t))}function no(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:h,setupState:p,ctx:y,inheritAttrs:w}=e,_=rs(e);let T,O;try{if(n.shapeFlag&4){const L=r||s,Y=L;T=at(u.call(Y,L,c,f,p,h,y)),O=l}else{const L=t;T=at(L.length>1?L(f,{attrs:l,slots:i,emit:a}):L(f,null)),O=t.props?l:vc(l)}}catch(L){Sn.length=0,xs(L,e,1),T=me(xt)}let I=T;if(O&&w!==!1){const L=Object.keys(O),{shapeFlag:Y}=I;L.length&&Y&7&&(o&&L.some(vr)&&(O=xc(O,o)),I=on(I,O,!1,!0))}return n.dirs&&(I=on(I,null,!1,!0),I.dirs=I.dirs?I.dirs.concat(n.dirs):n.dirs),n.transition&&Or(I,n.transition),T=I,rs(_),T}const vc=e=>{let t;for(const n in e)(n==="class"||n==="style"||ds(n))&&((t||(t={}))[n]=e[n]);return t},xc=(e,t)=>{const n={};for(const s in e)(!vr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function wc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?so(s,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const h=c[f];if(i[h]!==s[h]&&!_s(u,h))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?so(s,i,u):!0:!!i;return!1}function so(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!_s(n,o))return!0}return!1}function _c({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Qi=e=>e.__isSuspense;function Cc(e,t){t&&t.pendingBranch?X(e)?t.effects.push(...e):t.effects.push(e):$a(e)}const xe=Symbol.for("v-fgt"),Cs=Symbol.for("v-txt"),xt=Symbol.for("v-cmt"),Jn=Symbol.for("v-stc"),Sn=[];let qe=null;function V(e=!1){Sn.push(qe=e?null:[])}function Sc(){Sn.pop(),qe=Sn[Sn.length-1]||null}let On=1;function ro(e,t=!1){On+=e,e<0&&qe&&t&&(qe.hasOnce=!0)}function el(e){return e.dynamicChildren=On>0?qe||Yt:null,Sc(),On>0&&qe&&qe.push(e),e}function J(e,t,n,s,r,o){return el(g(e,t,n,s,r,o,!0))}function Ut(e,t,n,s,r){return el(me(e,t,n,s,r,!0))}function Fn(e){return e?e.__v_isVNode===!0:!1}function hn(e,t){return e.type===t.type&&e.key===t.key}const tl=({key:e})=>e??null,Gn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?we(e)||Pe(e)||te(e)?{i:Se,r:e,k:t,f:!!n}:e:null);function g(e,t=null,n=null,s=0,r=null,o=e===xe?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&tl(t),ref:t&&Gn(t),scopeId:Ai,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Se};return l?(Mr(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=we(n)?8:16),On>0&&!i&&qe&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&qe.push(a),a}const me=Rc;function Rc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Mi)&&(e=xt),Fn(e)){const l=on(e,t,!0);return n&&Mr(l,n),On>0&&!o&&qe&&(l.shapeFlag&6?qe[qe.indexOf(e)]=l:qe.push(l)),l.patchFlag=-2,l}if(Lc(e)&&(e=e.__vccOpts),t){t=Ec(t);let{class:l,style:a}=t;l&&!we(l)&&(t.class=Te(l)),be(a)&&(Tr(a)&&!X(a)&&(a=Fe({},a)),t.style=ys(a))}const i=we(e)?1:Qi(e)?128:Ia(e)?64:be(e)?4:te(e)?2:0;return g(e,t,n,s,r,i,o,!0)}function Ec(e){return e?Tr(e)||Bi(e)?Fe({},e):e:null}function on(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?Ac(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&tl(u),ref:t&&t.ref?n&&o?X(o)?o.concat(Gn(t)):[o,Gn(t)]:Gn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==xe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&on(e.ssContent),ssFallback:e.ssFallback&&on(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&Or(c,a.clone(c)),c}function Me(e=" ",t=0){return me(Cs,null,e,t)}function We(e,t){const n=me(Jn,null,e);return n.staticCount=t,n}function Oe(e="",t=!1){return t?(V(),Ut(xt,null,e)):me(xt,null,e)}function at(e){return e==null||typeof e=="boolean"?me(xt):X(e)?me(xe,null,e.slice()):Fn(e)?Et(e):me(Cs,null,String(e))}function Et(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:on(e)}function Mr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(X(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Mr(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Bi(t)?t._ctx=Se:r===3&&Se&&(Se.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else te(t)?(t={default:t,_ctx:Se},n=32):(t=String(t),s&64?(n=16,t=[Me(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ac(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Te([t.class,s.class]));else if(r==="style")t.style=ys([t.style,s.style]);else if(ds(r)){const o=t[r],i=s[r];i&&o!==i&&!(X(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function ot(e,t,n,s=null){ut(e,t,7,[n,s])}const Tc=Ui();let Pc=0;function Oc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Tc,o={uid:Pc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new sa(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qi(s,r),emitsOptions:Yi(s,r),emit:null,emitted:null,propsDefaults:he,inheritAttrs:s.inheritAttrs,ctx:he,data:he,props:he,attrs:he,slots:he,refs:he,setupState:he,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=bc.bind(null,o),e.ce&&e.ce(o),o}let Ee=null,ls,lr;{const e=gs(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};ls=t("__VUE_INSTANCE_SETTERS__",n=>Ee=n),lr=t("__VUE_SSR_SETTERS__",n=>kn=n)}const Un=e=>{const t=Ee;return ls(e),e.scope.on(),()=>{e.scope.off(),ls(t)}},oo=()=>{Ee&&Ee.scope.off(),ls(null)};function nl(e){return e.vnode.shapeFlag&4}let kn=!1;function Fc(e,t=!1,n=!1){t&&lr(t);const{props:s,children:r}=e.vnode,o=nl(e);rc(e,s,o,t),ac(e,r,n||t);const i=o?kc(e,t):void 0;return t&&lr(!1),i}function kc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Za);const{setup:s}=n;if(s){bt();const r=e.setupContext=s.length>1?Mc(e):null,o=Un(e),i=Hn(s,e,0,[e.props,r]),l=Yo(i);if(vt(),o(),(l||e.sp)&&!nn(e)&&Oi(e),l){if(i.then(oo,oo),t)return i.then(a=>{io(e,a)}).catch(a=>{xs(a,e,0)});e.asyncDep=i}else io(e,i)}else sl(e)}function io(e,t,n){te(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:be(t)&&(e.setupState=_i(t)),sl(e)}function sl(e,t,n){const s=e.type;e.render||(e.render=s.render||ct);{const r=Un(e);bt();try{Xa(e)}finally{vt(),r()}}}const $c={get(e,t){return Re(e,"get",""),e[t]}};function Mc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,$c),slots:e.slots,emit:e.emit,expose:t}}function Ss(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(_i(Ca(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in _n)return _n[n](e)},has(t,n){return n in t||n in _n}})):e.proxy}function Ic(e,t=!0){return te(e)?e.displayName||e.name:e.name||t&&e.__name}function Lc(e){return te(e)&&"__vccOpts"in e}const ve=(e,t)=>Ta(e,t,kn);function rl(e,t,n){const s=arguments.length;return s===2?be(t)&&!X(t)?Fn(t)?me(e,null,[t]):me(e,t):me(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Fn(n)&&(n=[n]),me(e,t,n))}const Nc="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ar;const lo=typeof window<"u"&&window.trustedTypes;if(lo)try{ar=lo.createPolicy("vue",{createHTML:e=>e})}catch{}const ol=ar?e=>ar.createHTML(e):e=>e,Hc="http://www.w3.org/2000/svg",Uc="http://www.w3.org/1998/Math/MathML",mt=typeof document<"u"?document:null,ao=mt&&mt.createElement("template"),Dc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?mt.createElementNS(Hc,e):t==="mathml"?mt.createElementNS(Uc,e):n?mt.createElement(e,{is:n}):mt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>mt.createTextNode(e),createComment:e=>mt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>mt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{ao.innerHTML=ol(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=ao.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},jc=Symbol("_vtc");function Bc(e,t,n){const s=e[jc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const co=Symbol("_vod"),Vc=Symbol("_vsh"),qc=Symbol(""),zc=/(^|;)\s*display\s*:/;function Kc(e,t,n){const s=e.style,r=we(n);let o=!1;if(n&&!r){if(t)if(we(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Zn(s,l,"")}else for(const i in t)n[i]==null&&Zn(s,i,"");for(const i in n)i==="display"&&(o=!0),Zn(s,i,n[i])}else if(r){if(t!==n){const i=s[qc];i&&(n+=";"+i),s.cssText=n,o=zc.test(n)}}else t&&e.removeAttribute("style");co in e&&(e[co]=o?s.display:"",e[Vc]&&(s.display="none"))}const uo=/\s*!important$/;function Zn(e,t,n){if(X(n))n.forEach(s=>Zn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Wc(e,t);uo.test(n)?e.setProperty(Bt(s),n.replace(uo,""),"important"):e[s]=n}}const fo=["Webkit","Moz","ms"],Vs={};function Wc(e,t){const n=Vs[t];if(n)return n;let s=Je(t);if(s!=="filter"&&s in e)return Vs[t]=s;s=ms(s);for(let r=0;r<fo.length;r++){const o=fo[r]+s;if(o in e)return Vs[t]=o}return t}const po="http://www.w3.org/1999/xlink";function ho(e,t,n,s,r,o=ea(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(po,t.slice(6,t.length)):e.setAttributeNS(po,t,n):n==null||o&&!ni(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Xe(n)?String(n):n)}function mo(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ol(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ni(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Mt(e,t,n,s){e.addEventListener(t,n,s)}function Jc(e,t,n,s){e.removeEventListener(t,n,s)}const go=Symbol("_vei");function Gc(e,t,n,s,r=null){const o=e[go]||(e[go]={}),i=o[t];if(s&&i)i.value=s;else{const[l,a]=Zc(t);if(s){const u=o[t]=Qc(s,r);Mt(e,l,u,a)}else i&&(Jc(e,l,i,a),o[t]=void 0)}}const yo=/(?:Once|Passive|Capture)$/;function Zc(e){let t;if(yo.test(e)){t={};let s;for(;s=e.match(yo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Bt(e.slice(2)),t]}let qs=0;const Xc=Promise.resolve(),Yc=()=>qs||(Xc.then(()=>qs=0),qs=Date.now());function Qc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ut(eu(s,n.value),t,5,[s])};return n.value=e,n.attached=Yc(),n}function eu(e,t){if(X(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const bo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,tu=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Bc(e,s,i):t==="style"?Kc(e,n,s):ds(t)?vr(t)||Gc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):nu(e,t,s,i))?(mo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ho(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!we(s))?mo(e,Je(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ho(e,t,s,i))};function nu(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&bo(t)&&te(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return bo(t)&&we(n)?!1:t in e}const as=e=>{const t=e.props["onUpdate:modelValue"]||!1;return X(t)?n=>Kn(t,n):t};function su(e){e.target.composing=!0}function vo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const rn=Symbol("_assign"),ru={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[rn]=as(r);const o=s||r.props&&r.props.type==="number";Mt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=es(l)),e[rn](l)}),n&&Mt(e,"change",()=>{e.value=e.value.trim()}),t||(Mt(e,"compositionstart",su),Mt(e,"compositionend",vo),Mt(e,"change",vo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[rn]=as(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?es(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===a)||(e.value=a))}},ln={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=ps(t);Mt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?es(cs(i)):cs(i));e[rn](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,Vt(()=>{e._assigning=!1})}),e[rn]=as(s)},mounted(e,{value:t}){xo(e,t)},beforeUpdate(e,t,n){e[rn]=as(n)},updated(e,{value:t}){e._assigning||xo(e,t)}};function xo(e,t){const n=e.multiple,s=X(t);if(!(n&&!s&&!ps(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=cs(i);if(n)if(s){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(u=>String(u)===String(l)):i.selected=na(t,l)>-1}else i.selected=t.has(l);else if(bs(cs(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function cs(e){return"_value"in e?e._value:e.value}const ou=Fe({patchProp:tu},Dc);let wo;function iu(){return wo||(wo=uc(ou))}const lu=(...e)=>{const t=iu().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=cu(s);if(!r)return;const o=t._component;!te(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,au(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function au(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function cu(e){return we(e)?document.querySelector(e):e}const uu={__name:"App",setup(e){return(t,n)=>{const s=Wa("router-view");return V(),Ut(s)}}};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Xt=typeof document<"u";function il(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function fu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&il(e.default)}const ce=Object.assign;function zs(e,t){const n={};for(const s in t){const r=t[s];n[s]=Qe(r)?r.map(e):e(r)}return n}const Rn=()=>{},Qe=Array.isArray,ll=/#/g,du=/&/g,pu=/\//g,hu=/=/g,mu=/\?/g,al=/\+/g,gu=/%5B/g,yu=/%5D/g,cl=/%5E/g,bu=/%60/g,ul=/%7B/g,vu=/%7C/g,fl=/%7D/g,xu=/%20/g;function Ir(e){return encodeURI(""+e).replace(vu,"|").replace(gu,"[").replace(yu,"]")}function wu(e){return Ir(e).replace(ul,"{").replace(fl,"}").replace(cl,"^")}function cr(e){return Ir(e).replace(al,"%2B").replace(xu,"+").replace(ll,"%23").replace(du,"%26").replace(bu,"`").replace(ul,"{").replace(fl,"}").replace(cl,"^")}function _u(e){return cr(e).replace(hu,"%3D")}function Cu(e){return Ir(e).replace(ll,"%23").replace(mu,"%3F")}function Su(e){return e==null?"":Cu(e).replace(pu,"%2F")}function $n(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Ru=/\/$/,Eu=e=>e.replace(Ru,"");function Ks(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Ou(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:$n(i)}}function Au(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function _o(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Tu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&an(t.matched[s],n.matched[r])&&dl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function an(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function dl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Pu(e[n],t[n]))return!1;return!0}function Pu(e,t){return Qe(e)?Co(e,t):Qe(t)?Co(t,e):e===t}function Co(e,t){return Qe(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Ou(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const Ct={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Mn;(function(e){e.pop="pop",e.push="push"})(Mn||(Mn={}));var En;(function(e){e.back="back",e.forward="forward",e.unknown=""})(En||(En={}));function Fu(e){if(!e)if(Xt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Eu(e)}const ku=/^[^#]+#/;function $u(e,t){return e.replace(ku,"#")+t}function Mu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Rs=()=>({left:window.scrollX,top:window.scrollY});function Iu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Mu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function So(e,t){return(history.state?history.state.position-t:-1)+e}const ur=new Map;function Lu(e,t){ur.set(e,t)}function Nu(e){const t=ur.get(e);return ur.delete(e),t}let Hu=()=>location.protocol+"//"+location.host;function pl(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),_o(a,"")}return _o(n,e)+s+r}function Uu(e,t,n,s){let r=[],o=[],i=null;const l=({state:h})=>{const p=pl(e,location),y=n.value,w=t.value;let _=0;if(h){if(n.value=p,t.value=h,i&&i===y){i=null;return}_=w?h.position-w.position:0}else s(p);r.forEach(T=>{T(n.value,y,{delta:_,type:Mn.pop,direction:_?_>0?En.forward:En.back:En.unknown})})};function a(){i=n.value}function u(h){r.push(h);const p=()=>{const y=r.indexOf(h);y>-1&&r.splice(y,1)};return o.push(p),p}function c(){const{history:h}=window;h.state&&h.replaceState(ce({},h.state,{scroll:Rs()}),"")}function f(){for(const h of o)h();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function Ro(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Rs():null}}function Du(e){const{history:t,location:n}=window,s={value:pl(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:Hu()+e+a;try{t[c?"replaceState":"pushState"](u,"",h),r.value=u}catch(p){console.error(p),n[c?"replace":"assign"](h)}}function i(a,u){const c=ce({},t.state,Ro(r.value.back,a,r.value.forward,!0),u,{position:r.value.position});o(a,c,!0),s.value=a}function l(a,u){const c=ce({},r.value,t.state,{forward:a,scroll:Rs()});o(c.current,c,!0);const f=ce({},Ro(s.value,a,null),{position:c.position+1},u);o(a,f,!1),s.value=a}return{location:s,state:r,push:l,replace:i}}function ju(e){e=Fu(e);const t=Du(e),n=Uu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=ce({location:"",base:e,go:s,createHref:$u.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Bu(e){return typeof e=="string"||e&&typeof e=="object"}function hl(e){return typeof e=="string"||typeof e=="symbol"}const ml=Symbol("");var Eo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Eo||(Eo={}));function cn(e,t){return ce(new Error,{type:e,[ml]:!0},t)}function pt(e,t){return e instanceof Error&&ml in e&&(t==null||!!(e.type&t))}const Ao="[^/]+?",Vu={sensitive:!1,strict:!1,start:!0,end:!0},qu=/[.+*?^${}()[\]/\\]/g;function zu(e,t){const n=ce({},Vu,t),s=[];let r=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const h=u[f];let p=40+(n.sensitive?.25:0);if(h.type===0)f||(r+="/"),r+=h.value.replace(qu,"\\$&"),p+=40;else if(h.type===1){const{value:y,repeatable:w,optional:_,regexp:T}=h;o.push({name:y,repeatable:w,optional:_});const O=T||Ao;if(O!==Ao){p+=10;try{new RegExp(`(${O})`)}catch(L){throw new Error(`Invalid custom RegExp for param "${y}" (${O}): `+L.message)}}let I=w?`((?:${O})(?:/(?:${O}))*)`:`(${O})`;f||(I=_&&u.length<2?`(?:/${I})`:"/"+I),_&&(I+="?"),r+=I,p+=20,_&&(p+=-8),w&&(p+=-20),O===".*"&&(p+=-50)}c.push(p)}s.push(c)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let h=1;h<c.length;h++){const p=c[h]||"",y=o[h-1];f[y.name]=p&&y.repeatable?p.split("/"):p}return f}function a(u){let c="",f=!1;for(const h of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const p of h)if(p.type===0)c+=p.value;else if(p.type===1){const{value:y,repeatable:w,optional:_}=p,T=y in u?u[y]:"";if(Qe(T)&&!w)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const O=Qe(T)?T.join("/"):T;if(!O)if(_)h.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);c+=O}}return c||"/"}return{re:i,score:s,keys:o,parse:l,stringify:a}}function Ku(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function gl(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Ku(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(To(s))return 1;if(To(r))return-1}return r.length-s.length}function To(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Wu={type:0,value:""},Ju=/[a-zA-Z0-9_]/;function Gu(e){if(!e)return[[]];if(e==="/")return[[Wu]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${u}": ${p}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function h(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:a==="("?n=2:Ju.test(a)?h():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function Zu(e,t,n){const s=zu(Gu(e.path),n),r=ce(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Xu(e,t){const n=[],s=new Map;t=ko({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,h,p){const y=!p,w=Oo(f);w.aliasOf=p&&p.record;const _=ko(t,f),T=[w];if("alias"in f){const L=typeof f.alias=="string"?[f.alias]:f.alias;for(const Y of L)T.push(Oo(ce({},w,{components:p?p.record.components:w.components,path:Y,aliasOf:p?p.record:w})))}let O,I;for(const L of T){const{path:Y}=L;if(h&&Y[0]!=="/"){const le=h.record.path,oe=le[le.length-1]==="/"?"":"/";L.path=h.record.path+(Y&&oe+Y)}if(O=Zu(L,h,_),p?p.alias.push(O):(I=I||O,I!==O&&I.alias.push(O),y&&f.name&&!Fo(O)&&i(f.name)),yl(O)&&a(O),w.children){const le=w.children;for(let oe=0;oe<le.length;oe++)o(le[oe],O,p&&p.children[oe])}p=p||O}return I?()=>{i(I)}:Rn}function i(f){if(hl(f)){const h=s.get(f);h&&(s.delete(f),n.splice(n.indexOf(h),1),h.children.forEach(i),h.alias.forEach(i))}else{const h=n.indexOf(f);h>-1&&(n.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const h=ef(f,n);n.splice(h,0,f),f.record.name&&!Fo(f)&&s.set(f.record.name,f)}function u(f,h){let p,y={},w,_;if("name"in f&&f.name){if(p=s.get(f.name),!p)throw cn(1,{location:f});_=p.record.name,y=ce(Po(h.params,p.keys.filter(I=>!I.optional).concat(p.parent?p.parent.keys.filter(I=>I.optional):[]).map(I=>I.name)),f.params&&Po(f.params,p.keys.map(I=>I.name))),w=p.stringify(y)}else if(f.path!=null)w=f.path,p=n.find(I=>I.re.test(w)),p&&(y=p.parse(w),_=p.record.name);else{if(p=h.name?s.get(h.name):n.find(I=>I.re.test(h.path)),!p)throw cn(1,{location:f,currentLocation:h});_=p.record.name,y=ce({},h.params,f.params),w=p.stringify(y)}const T=[];let O=p;for(;O;)T.unshift(O.record),O=O.parent;return{name:_,path:w,params:y,matched:T,meta:Qu(T)}}e.forEach(f=>o(f));function c(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function Po(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Oo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Yu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Yu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Fo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Qu(e){return e.reduce((t,n)=>ce(t,n.meta),{})}function ko(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function ef(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;gl(e,t[o])<0?s=o:n=o+1}const r=tf(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function tf(e){let t=e;for(;t=t.parent;)if(yl(t)&&gl(e,t)===0)return t}function yl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function nf(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(al," "),i=o.indexOf("="),l=$n(i<0?o:o.slice(0,i)),a=i<0?null:$n(o.slice(i+1));if(l in t){let u=t[l];Qe(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function $o(e){let t="";for(let n in e){const s=e[n];if(n=_u(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Qe(s)?s.map(o=>o&&cr(o)):[s&&cr(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function sf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Qe(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const rf=Symbol(""),Mo=Symbol(""),Es=Symbol(""),Lr=Symbol(""),fr=Symbol("");function mn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function At(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const u=h=>{h===!1?a(cn(4,{from:n,to:t})):h instanceof Error?a(h):Bu(h)?a(cn(2,{from:t,to:h})):(i&&s.enterCallbacks[r]===i&&typeof h=="function"&&i.push(h),l())},c=o(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(h=>a(h))})}function Ws(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(il(a)){const c=(a.__vccOpts||a)[t];c&&o.push(At(c,n,s,i,l,r))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=fu(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const p=(f.__vccOpts||f)[t];return p&&At(p,n,s,i,l,r)()}))}}return o}function Io(e){const t=Ze(Es),n=Ze(Lr),s=ve(()=>{const a=Ve(e.to);return t.resolve(a)}),r=ve(()=>{const{matched:a}=s.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const h=f.findIndex(an.bind(null,c));if(h>-1)return h;const p=Lo(a[u-2]);return u>1&&Lo(c)===p&&f[f.length-1].path!==p?f.findIndex(an.bind(null,a[u-2])):h}),o=ve(()=>r.value>-1&&uf(n.params,s.value.params)),i=ve(()=>r.value>-1&&r.value===n.matched.length-1&&dl(n.params,s.value.params));function l(a={}){if(cf(a)){const u=t[Ve(e.replace)?"replace":"push"](Ve(e.to)).catch(Rn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:ve(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function of(e){return e.length===1?e[0]:e}const lf=Pi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Io,setup(e,{slots:t}){const n=Nn(Io(e)),{options:s}=Ze(Es),r=ve(()=>({[No(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[No(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&of(t.default(n));return e.custom?o:rl("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),af=lf;function cf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function uf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Qe(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Lo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const No=(e,t,n)=>e??t??n,ff=Pi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ze(fr),r=ve(()=>e.route||s.value),o=Ze(Mo,0),i=ve(()=>{let u=Ve(o);const{matched:c}=r.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=ve(()=>r.value.matched[i.value]);Wn(Mo,ve(()=>i.value+1)),Wn(rf,l),Wn(fr,r);const a=Z();return Cn(()=>[a.value,l.value,e.name],([u,c,f],[h,p,y])=>{c&&(c.instances[f]=u,p&&p!==c&&u&&u===h&&(c.leaveGuards.size||(c.leaveGuards=p.leaveGuards),c.updateGuards.size||(c.updateGuards=p.updateGuards))),u&&c&&(!p||!an(c,p)||!h)&&(c.enterCallbacks[f]||[]).forEach(w=>w(u))},{flush:"post"}),()=>{const u=r.value,c=e.name,f=l.value,h=f&&f.components[c];if(!h)return Ho(n.default,{Component:h,route:u});const p=f.props[c],y=p?p===!0?u.params:typeof p=="function"?p(u):p:null,_=rl(h,ce({},y,t,{onVnodeUnmounted:T=>{T.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return Ho(n.default,{Component:_,route:u})||_}}});function Ho(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const df=ff;function pf(e){const t=Xu(e.routes,e),n=e.parseQuery||nf,s=e.stringifyQuery||$o,r=e.history,o=mn(),i=mn(),l=mn(),a=Sa(Ct);let u=Ct;Xt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=zs.bind(null,R=>""+R),f=zs.bind(null,Su),h=zs.bind(null,$n);function p(R,j){let H,z;return hl(R)?(H=t.getRecordMatcher(R),z=j):z=R,t.addRoute(z,H)}function y(R){const j=t.getRecordMatcher(R);j&&t.removeRoute(j)}function w(){return t.getRoutes().map(R=>R.record)}function _(R){return!!t.getRecordMatcher(R)}function T(R,j){if(j=ce({},j||a.value),typeof R=="string"){const b=Ks(n,R,j.path),S=t.resolve({path:b.path},j),A=r.createHref(b.fullPath);return ce(b,S,{params:h(S.params),hash:$n(b.hash),redirectedFrom:void 0,href:A})}let H;if(R.path!=null)H=ce({},R,{path:Ks(n,R.path,j.path).path});else{const b=ce({},R.params);for(const S in b)b[S]==null&&delete b[S];H=ce({},R,{params:f(b)}),j.params=f(j.params)}const z=t.resolve(H,j),pe=R.hash||"";z.params=c(h(z.params));const d=Au(s,ce({},R,{hash:wu(pe),path:z.path})),m=r.createHref(d);return ce({fullPath:d,hash:pe,query:s===$o?sf(R.query):R.query||{}},z,{redirectedFrom:void 0,href:m})}function O(R){return typeof R=="string"?Ks(n,R,a.value.path):ce({},R)}function I(R,j){if(u!==R)return cn(8,{from:j,to:R})}function L(R){return oe(R)}function Y(R){return L(ce(O(R),{replace:!0}))}function le(R){const j=R.matched[R.matched.length-1];if(j&&j.redirect){const{redirect:H}=j;let z=typeof H=="function"?H(R):H;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=O(z):{path:z},z.params={}),ce({query:R.query,hash:R.hash,params:z.path!=null?{}:R.params},z)}}function oe(R,j){const H=u=T(R),z=a.value,pe=R.state,d=R.force,m=R.replace===!0,b=le(H);if(b)return oe(ce(O(b),{state:typeof b=="object"?ce({},pe,b.state):pe,force:d,replace:m}),j||H);const S=H;S.redirectedFrom=j;let A;return!d&&Tu(s,z,H)&&(A=cn(16,{to:S,from:z}),tt(z,z,!0,!1)),(A?Promise.resolve(A):C(S,z)).catch(E=>pt(E)?pt(E,2)?E:_t(E):re(E,S,z)).then(E=>{if(E){if(pt(E,2))return oe(ce({replace:m},O(E.to),{state:typeof E.to=="object"?ce({},pe,E.to.state):pe,force:d}),j||S)}else E=F(S,z,!0,m,pe);return k(S,z,E),E})}function q(R,j){const H=I(R,j);return H?Promise.reject(H):Promise.resolve()}function v(R){const j=Kt.values().next().value;return j&&typeof j.runWithContext=="function"?j.runWithContext(R):R()}function C(R,j){let H;const[z,pe,d]=hf(R,j);H=Ws(z.reverse(),"beforeRouteLeave",R,j);for(const b of z)b.leaveGuards.forEach(S=>{H.push(At(S,R,j))});const m=q.bind(null,R,j);return H.push(m),ze(H).then(()=>{H=[];for(const b of o.list())H.push(At(b,R,j));return H.push(m),ze(H)}).then(()=>{H=Ws(pe,"beforeRouteUpdate",R,j);for(const b of pe)b.updateGuards.forEach(S=>{H.push(At(S,R,j))});return H.push(m),ze(H)}).then(()=>{H=[];for(const b of d)if(b.beforeEnter)if(Qe(b.beforeEnter))for(const S of b.beforeEnter)H.push(At(S,R,j));else H.push(At(b.beforeEnter,R,j));return H.push(m),ze(H)}).then(()=>(R.matched.forEach(b=>b.enterCallbacks={}),H=Ws(d,"beforeRouteEnter",R,j,v),H.push(m),ze(H))).then(()=>{H=[];for(const b of i.list())H.push(At(b,R,j));return H.push(m),ze(H)}).catch(b=>pt(b,8)?b:Promise.reject(b))}function k(R,j,H){l.list().forEach(z=>v(()=>z(R,j,H)))}function F(R,j,H,z,pe){const d=I(R,j);if(d)return d;const m=j===Ct,b=Xt?history.state:{};H&&(z||m?r.replace(R.fullPath,ce({scroll:m&&b&&b.scroll},pe)):r.push(R.fullPath,pe)),a.value=R,tt(R,j,H,m),_t()}let D;function B(){D||(D=r.listen((R,j,H)=>{if(!Bn.listening)return;const z=T(R),pe=le(z);if(pe){oe(ce(pe,{replace:!0,force:!0}),z).catch(Rn);return}u=z;const d=a.value;Xt&&Lu(So(d.fullPath,H.delta),Rs()),C(z,d).catch(m=>pt(m,12)?m:pt(m,2)?(oe(ce(O(m.to),{force:!0}),z).then(b=>{pt(b,20)&&!H.delta&&H.type===Mn.pop&&r.go(-1,!1)}).catch(Rn),Promise.reject()):(H.delta&&r.go(-H.delta,!1),re(m,z,d))).then(m=>{m=m||F(z,d,!1),m&&(H.delta&&!pt(m,8)?r.go(-H.delta,!1):H.type===Mn.pop&&pt(m,20)&&r.go(-1,!1)),k(z,d,m)}).catch(Rn)}))}let K=mn(),se=mn(),ee;function re(R,j,H){_t(R);const z=se.list();return z.length?z.forEach(pe=>pe(R,j,H)):console.error(R),Promise.reject(R)}function ft(){return ee&&a.value!==Ct?Promise.resolve():new Promise((R,j)=>{K.add([R,j])})}function _t(R){return ee||(ee=!R,B(),K.list().forEach(([j,H])=>R?H(R):j()),K.reset()),R}function tt(R,j,H,z){const{scrollBehavior:pe}=e;if(!Xt||!pe)return Promise.resolve();const d=!H&&Nu(So(R.fullPath,0))||(z||!H)&&history.state&&history.state.scroll||null;return Vt().then(()=>pe(R,j,d)).then(m=>m&&Iu(m)).catch(m=>re(m,R,j))}const Ie=R=>r.go(R);let zt;const Kt=new Set,Bn={currentRoute:a,listening:!0,addRoute:p,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:w,resolve:T,options:e,push:L,replace:Y,go:Ie,back:()=>Ie(-1),forward:()=>Ie(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:se.add,isReady:ft,install(R){const j=this;R.component("RouterLink",af),R.component("RouterView",df),R.config.globalProperties.$router=j,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Ve(a)}),Xt&&!zt&&a.value===Ct&&(zt=!0,L(r.location).catch(pe=>{}));const H={};for(const pe in Ct)Object.defineProperty(H,pe,{get:()=>a.value[pe],enumerable:!0});R.provide(Es,j),R.provide(Lr,vi(H)),R.provide(fr,a);const z=R.unmount;Kt.add(R),R.unmount=function(){Kt.delete(R),Kt.size<1&&(u=Ct,D&&D(),D=null,a.value=Ct,zt=!1,ee=!1),z()}}};function ze(R){return R.reduce((j,H)=>j.then(()=>v(H)),Promise.resolve())}return Bn}function hf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>an(u,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>an(u,a))||r.push(a))}return[n,s,r]}function mf(){return Ze(Es)}function gf(e){return Ze(Lr)}const yf="/images/niswey-hubspot.png",bf="/images/HubSpot.png",vf={class:"flex justify-between items-center bg-gray-800 text-white h-8 px-4"},xf={class:"flex items-center space-x-2"},wf={class:"text-sm text-white"},_f={__name:"LoginHeader",setup(e){const t=gf(),n=ve(()=>t.query.email||"");return(s,r)=>(V(),J("div",null,[r[3]||(r[3]=g("div",{class:"h-10 bg-gradient-to-r from-cyan-500 to-green-400"},null,-1)),g("div",vf,[r[2]||(r[2]=g("div",{class:"flex items-center"},[g("img",{src:bf,alt:"HubSpot Logo",style:{height:"30px"}})],-1)),g("div",xf,[r[0]||(r[0]=g("div",{class:"bg-gray-700 p-1 rounded-full"},[g("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[g("path",{"fill-rule":"evenodd",d:"M10 3a5 5 0 100 10 5 5 0 000-10zM2 17a8 8 0 0116 0H2z","clip-rule":"evenodd"})])],-1)),g("div",wf,ae(n.value),1),r[1]||(r[1]=g("button",{class:"focus:outline-none"},[g("svg",{class:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20"},[g("path",{d:"M5.5 7l4.5 4.5L14.5 7h-9z"})])],-1))])])]))}},Cf=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Sf={},Rf={class:"bg-gray-800 text-white text-center p-4"};function Ef(e,t){return V(),J("footer",Rf,t[0]||(t[0]=[Me(" © Powered by "),g("a",{href:"https://niswey.com"},"Niswey",-1)]))}const Af=Cf(Sf,[["render",Ef]]),Tf={class:"flex flex-col min-h-screen"},Pf={class:"min-h-screen flex items-center justify-center"},Of={__name:"DefaultLayout",setup(e){return(t,n)=>(V(),J("div",Tf,[me(_f),g("main",Pf,[Ga(t.$slots,"default")]),me(Af)]))}};function bl(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ff}=Object.prototype,{getPrototypeOf:Nr}=Object,{iterator:As,toStringTag:vl}=Symbol,Ts=(e=>t=>{const n=Ff.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),et=e=>(e=e.toLowerCase(),t=>Ts(t)===e),Ps=e=>t=>typeof t===e,{isArray:un}=Array,In=Ps("undefined");function kf(e){return e!==null&&!In(e)&&e.constructor!==null&&!In(e.constructor)&&He(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const xl=et("ArrayBuffer");function $f(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&xl(e.buffer),t}const Mf=Ps("string"),He=Ps("function"),wl=Ps("number"),Os=e=>e!==null&&typeof e=="object",If=e=>e===!0||e===!1,Xn=e=>{if(Ts(e)!=="object")return!1;const t=Nr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(vl in e)&&!(As in e)},Lf=et("Date"),Nf=et("File"),Hf=et("Blob"),Uf=et("FileList"),Df=e=>Os(e)&&He(e.pipe),jf=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||He(e.append)&&((t=Ts(e))==="formdata"||t==="object"&&He(e.toString)&&e.toString()==="[object FormData]"))},Bf=et("URLSearchParams"),[Vf,qf,zf,Kf]=["ReadableStream","Request","Response","Headers"].map(et),Wf=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Dn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),un(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(s=0;s<i;s++)l=o[s],t.call(null,e[l],l,e)}}function _l(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const It=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Cl=e=>!In(e)&&e!==It;function dr(){const{caseless:e}=Cl(this)&&this||{},t={},n=(s,r)=>{const o=e&&_l(t,r)||r;Xn(t[o])&&Xn(s)?t[o]=dr(t[o],s):Xn(s)?t[o]=dr({},s):un(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Dn(arguments[s],n);return t}const Jf=(e,t,n,{allOwnKeys:s}={})=>(Dn(t,(r,o)=>{n&&He(r)?e[o]=bl(r,n):e[o]=r},{allOwnKeys:s}),e),Gf=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Zf=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Xf=(e,t,n,s)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&Nr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Yf=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Qf=e=>{if(!e)return null;if(un(e))return e;let t=e.length;if(!wl(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},ed=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Nr(Uint8Array)),td=(e,t)=>{const s=(e&&e[As]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},nd=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},sd=et("HTMLFormElement"),rd=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),Uo=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),od=et("RegExp"),Sl=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Dn(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},id=e=>{Sl(e,(t,n)=>{if(He(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(He(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ld=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return un(e)?s(e):s(String(e).split(t)),n},ad=()=>{},cd=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function ud(e){return!!(e&&He(e.append)&&e[vl]==="FormData"&&e[As])}const fd=e=>{const t=new Array(10),n=(s,r)=>{if(Os(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=un(s)?[]:{};return Dn(s,(i,l)=>{const a=n(i,r+1);!In(a)&&(o[l]=a)}),t[r]=void 0,o}}return s};return n(e,0)},dd=et("AsyncFunction"),pd=e=>e&&(Os(e)||He(e))&&He(e.then)&&He(e.catch),Rl=((e,t)=>e?setImmediate:t?((n,s)=>(It.addEventListener("message",({source:r,data:o})=>{r===It&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),It.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",He(It.postMessage)),hd=typeof queueMicrotask<"u"?queueMicrotask.bind(It):typeof process<"u"&&process.nextTick||Rl,md=e=>e!=null&&He(e[As]),x={isArray:un,isArrayBuffer:xl,isBuffer:kf,isFormData:jf,isArrayBufferView:$f,isString:Mf,isNumber:wl,isBoolean:If,isObject:Os,isPlainObject:Xn,isReadableStream:Vf,isRequest:qf,isResponse:zf,isHeaders:Kf,isUndefined:In,isDate:Lf,isFile:Nf,isBlob:Hf,isRegExp:od,isFunction:He,isStream:Df,isURLSearchParams:Bf,isTypedArray:ed,isFileList:Uf,forEach:Dn,merge:dr,extend:Jf,trim:Wf,stripBOM:Gf,inherits:Zf,toFlatObject:Xf,kindOf:Ts,kindOfTest:et,endsWith:Yf,toArray:Qf,forEachEntry:td,matchAll:nd,isHTMLForm:sd,hasOwnProperty:Uo,hasOwnProp:Uo,reduceDescriptors:Sl,freezeMethods:id,toObjectSet:ld,toCamelCase:rd,noop:ad,toFiniteNumber:cd,findKey:_l,global:It,isContextDefined:Cl,isSpecCompliantForm:ud,toJSONObject:fd,isAsyncFn:dd,isThenable:pd,setImmediate:Rl,asap:hd,isIterable:md};function ne(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}x.inherits(ne,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:x.toJSONObject(this.config),code:this.code,status:this.status}}});const El=ne.prototype,Al={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Al[e]={value:e}});Object.defineProperties(ne,Al);Object.defineProperty(El,"isAxiosError",{value:!0});ne.from=(e,t,n,s,r,o)=>{const i=Object.create(El);return x.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),ne.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const gd=null;function pr(e){return x.isPlainObject(e)||x.isArray(e)}function Tl(e){return x.endsWith(e,"[]")?e.slice(0,-2):e}function Do(e,t,n){return e?e.concat(t).map(function(r,o){return r=Tl(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function yd(e){return x.isArray(e)&&!e.some(pr)}const bd=x.toFlatObject(x,{},null,function(t){return/^is[A-Z]/.test(t)});function Fs(e,t,n){if(!x.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=x.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,_){return!x.isUndefined(_[w])});const s=n.metaTokens,r=n.visitor||c,o=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&x.isSpecCompliantForm(t);if(!x.isFunction(r))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(x.isDate(y))return y.toISOString();if(!a&&x.isBlob(y))throw new ne("Blob is not supported. Use a Buffer instead.");return x.isArrayBuffer(y)||x.isTypedArray(y)?a&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function c(y,w,_){let T=y;if(y&&!_&&typeof y=="object"){if(x.endsWith(w,"{}"))w=s?w:w.slice(0,-2),y=JSON.stringify(y);else if(x.isArray(y)&&yd(y)||(x.isFileList(y)||x.endsWith(w,"[]"))&&(T=x.toArray(y)))return w=Tl(w),T.forEach(function(I,L){!(x.isUndefined(I)||I===null)&&t.append(i===!0?Do([w],L,o):i===null?w:w+"[]",u(I))}),!1}return pr(y)?!0:(t.append(Do(_,w,o),u(y)),!1)}const f=[],h=Object.assign(bd,{defaultVisitor:c,convertValue:u,isVisitable:pr});function p(y,w){if(!x.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(y),x.forEach(y,function(T,O){(!(x.isUndefined(T)||T===null)&&r.call(t,T,x.isString(O)?O.trim():O,w,h))===!0&&p(T,w?w.concat(O):[O])}),f.pop()}}if(!x.isObject(e))throw new TypeError("data must be an object");return p(e),t}function jo(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Hr(e,t){this._pairs=[],e&&Fs(e,this,t)}const Pl=Hr.prototype;Pl.append=function(t,n){this._pairs.push([t,n])};Pl.toString=function(t){const n=t?function(s){return t.call(this,s,jo)}:jo;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function vd(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ol(e,t,n){if(!t)return e;const s=n&&n.encode||vd;x.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=x.isURLSearchParams(t)?t.toString():new Hr(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Bo{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){x.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Fl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},xd=typeof URLSearchParams<"u"?URLSearchParams:Hr,wd=typeof FormData<"u"?FormData:null,_d=typeof Blob<"u"?Blob:null,Cd={isBrowser:!0,classes:{URLSearchParams:xd,FormData:wd,Blob:_d},protocols:["http","https","file","blob","url","data"]},Ur=typeof window<"u"&&typeof document<"u",hr=typeof navigator=="object"&&navigator||void 0,Sd=Ur&&(!hr||["ReactNative","NativeScript","NS"].indexOf(hr.product)<0),Rd=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ed=Ur&&window.location.href||"http://localhost",Ad=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ur,hasStandardBrowserEnv:Sd,hasStandardBrowserWebWorkerEnv:Rd,navigator:hr,origin:Ed},Symbol.toStringTag,{value:"Module"})),Ae={...Ad,...Cd};function Td(e,t){return Fs(e,new Ae.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return Ae.isNode&&x.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Pd(e){return x.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Od(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function kl(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=n.length;return i=!i&&x.isArray(r)?r.length:i,a?(x.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!l):((!r[i]||!x.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&x.isArray(r[i])&&(r[i]=Od(r[i])),!l)}if(x.isFormData(e)&&x.isFunction(e.entries)){const n={};return x.forEachEntry(e,(s,r)=>{t(Pd(s),r,n,0)}),n}return null}function Fd(e,t,n){if(x.isString(e))try{return(t||JSON.parse)(e),x.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const jn={transitional:Fl,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=x.isObject(t);if(o&&x.isHTMLForm(t)&&(t=new FormData(t)),x.isFormData(t))return r?JSON.stringify(kl(t)):t;if(x.isArrayBuffer(t)||x.isBuffer(t)||x.isStream(t)||x.isFile(t)||x.isBlob(t)||x.isReadableStream(t))return t;if(x.isArrayBufferView(t))return t.buffer;if(x.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Td(t,this.formSerializer).toString();if((l=x.isFileList(t))||s.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return Fs(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),Fd(t)):t}],transformResponse:[function(t){const n=this.transitional||jn.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(x.isResponse(t)||x.isReadableStream(t))return t;if(t&&x.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?ne.from(l,ne.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ae.classes.FormData,Blob:Ae.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};x.forEach(["delete","get","head","post","put","patch"],e=>{jn.headers[e]={}});const kd=x.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),$d=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&kd[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Vo=Symbol("internals");function gn(e){return e&&String(e).trim().toLowerCase()}function Yn(e){return e===!1||e==null?e:x.isArray(e)?e.map(Yn):String(e)}function Md(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Id=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Js(e,t,n,s,r){if(x.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!x.isString(t)){if(x.isString(s))return t.indexOf(s)!==-1;if(x.isRegExp(s))return s.test(t)}}function Ld(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Nd(e,t){const n=x.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}let Ue=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(l,a,u){const c=gn(a);if(!c)throw new Error("header name must be a non-empty string");const f=x.findKey(r,c);(!f||r[f]===void 0||u===!0||u===void 0&&r[f]!==!1)&&(r[f||a]=Yn(l))}const i=(l,a)=>x.forEach(l,(u,c)=>o(u,c,a));if(x.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(x.isString(t)&&(t=t.trim())&&!Id(t))i($d(t),n);else if(x.isObject(t)&&x.isIterable(t)){let l={},a,u;for(const c of t){if(!x.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?x.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=gn(t),t){const s=x.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Md(r);if(x.isFunction(n))return n.call(this,r,s);if(x.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=gn(t),t){const s=x.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Js(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=gn(i),i){const l=x.findKey(s,i);l&&(!n||Js(s,s[l],l,n))&&(delete s[l],r=!0)}}return x.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||Js(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return x.forEach(this,(r,o)=>{const i=x.findKey(s,o);if(i){n[i]=Yn(r),delete n[o];return}const l=t?Ld(o):String(o).trim();l!==o&&delete n[o],n[l]=Yn(r),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return x.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&x.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[Vo]=this[Vo]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=gn(i);s[l]||(Nd(r,i),s[l]=!0)}return x.isArray(t)?t.forEach(o):o(t),this}};Ue.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);x.reduceDescriptors(Ue.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});x.freezeMethods(Ue);function Gs(e,t){const n=this||jn,s=t||n,r=Ue.from(s.headers);let o=s.data;return x.forEach(e,function(l){o=l.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function $l(e){return!!(e&&e.__CANCEL__)}function fn(e,t,n){ne.call(this,e??"canceled",ne.ERR_CANCELED,t,n),this.name="CanceledError"}x.inherits(fn,ne,{__CANCEL__:!0});function Ml(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new ne("Request failed with status code "+n.status,[ne.ERR_BAD_REQUEST,ne.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Hd(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ud(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=s[o];i||(i=u),n[r]=a,s[r]=u;let f=o,h=0;for(;f!==r;)h+=n[f++],f=f%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const p=c&&u-c;return p?Math.round(h*1e3/p):void 0}}function Dd(e,t){let n=0,s=1e3/t,r,o;const i=(u,c=Date.now())=>{n=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=s?i(u,c):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},s-f)))},()=>r&&i(r)]}const us=(e,t,n=3)=>{let s=0;const r=Ud(50,250);return Dd(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-s,u=r(a),c=i<=l;s=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},qo=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},zo=e=>(...t)=>x.asap(()=>e(...t)),jd=Ae.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ae.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ae.origin),Ae.navigator&&/(msie|trident)/i.test(Ae.navigator.userAgent)):()=>!0,Bd=Ae.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];x.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),x.isString(s)&&i.push("path="+s),x.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Vd(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function qd(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Il(e,t,n){let s=!Vd(t);return e&&(s||n==!1)?qd(e,t):t}const Ko=e=>e instanceof Ue?{...e}:e;function Dt(e,t){t=t||{};const n={};function s(u,c,f,h){return x.isPlainObject(u)&&x.isPlainObject(c)?x.merge.call({caseless:h},u,c):x.isPlainObject(c)?x.merge({},c):x.isArray(c)?c.slice():c}function r(u,c,f,h){if(x.isUndefined(c)){if(!x.isUndefined(u))return s(void 0,u,f,h)}else return s(u,c,f,h)}function o(u,c){if(!x.isUndefined(c))return s(void 0,c)}function i(u,c){if(x.isUndefined(c)){if(!x.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function l(u,c,f){if(f in t)return s(u,c);if(f in e)return s(void 0,u)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>r(Ko(u),Ko(c),f,!0)};return x.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=a[c]||r,h=f(e[c],t[c],c);x.isUndefined(h)&&f!==l||(n[c]=h)}),n}const Ll=e=>{const t=Dt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Ue.from(i),t.url=Ol(Il(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(x.isFormData(n)){if(Ae.hasStandardBrowserEnv||Ae.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ae.hasStandardBrowserEnv&&(s&&x.isFunction(s)&&(s=s(t)),s||s!==!1&&jd(t.url))){const u=r&&o&&Bd.read(o);u&&i.set(r,u)}return t},zd=typeof XMLHttpRequest<"u",Kd=zd&&function(e){return new Promise(function(n,s){const r=Ll(e);let o=r.data;const i=Ue.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=r,c,f,h,p,y;function w(){p&&p(),y&&y(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let _=new XMLHttpRequest;_.open(r.method.toUpperCase(),r.url,!0),_.timeout=r.timeout;function T(){if(!_)return;const I=Ue.from("getAllResponseHeaders"in _&&_.getAllResponseHeaders()),Y={data:!l||l==="text"||l==="json"?_.responseText:_.response,status:_.status,statusText:_.statusText,headers:I,config:e,request:_};Ml(function(oe){n(oe),w()},function(oe){s(oe),w()},Y),_=null}"onloadend"in _?_.onloadend=T:_.onreadystatechange=function(){!_||_.readyState!==4||_.status===0&&!(_.responseURL&&_.responseURL.indexOf("file:")===0)||setTimeout(T)},_.onabort=function(){_&&(s(new ne("Request aborted",ne.ECONNABORTED,e,_)),_=null)},_.onerror=function(){s(new ne("Network Error",ne.ERR_NETWORK,e,_)),_=null},_.ontimeout=function(){let L=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const Y=r.transitional||Fl;r.timeoutErrorMessage&&(L=r.timeoutErrorMessage),s(new ne(L,Y.clarifyTimeoutError?ne.ETIMEDOUT:ne.ECONNABORTED,e,_)),_=null},o===void 0&&i.setContentType(null),"setRequestHeader"in _&&x.forEach(i.toJSON(),function(L,Y){_.setRequestHeader(Y,L)}),x.isUndefined(r.withCredentials)||(_.withCredentials=!!r.withCredentials),l&&l!=="json"&&(_.responseType=r.responseType),u&&([h,y]=us(u,!0),_.addEventListener("progress",h)),a&&_.upload&&([f,p]=us(a),_.upload.addEventListener("progress",f),_.upload.addEventListener("loadend",p)),(r.cancelToken||r.signal)&&(c=I=>{_&&(s(!I||I.type?new fn(null,e,_):I),_.abort(),_=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const O=Hd(r.url);if(O&&Ae.protocols.indexOf(O)===-1){s(new ne("Unsupported protocol "+O+":",ne.ERR_BAD_REQUEST,e));return}_.send(o||null)})},Wd=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(u){if(!r){r=!0,l();const c=u instanceof Error?u:this.reason;s.abort(c instanceof ne?c:new fn(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new ne(`timeout ${t} of ms exceeded`,ne.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=s;return a.unsubscribe=()=>x.asap(l),a}},Jd=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},Gd=async function*(e,t){for await(const n of Zd(e))yield*Jd(n,t)},Zd=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Wo=(e,t,n,s)=>{const r=Gd(e,t);let o=0,i,l=a=>{i||(i=!0,s&&s(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await r.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let h=o+=f;n(h)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},ks=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Nl=ks&&typeof ReadableStream=="function",Xd=ks&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Hl=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Yd=Nl&&Hl(()=>{let e=!1;const t=new Request(Ae.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Jo=64*1024,mr=Nl&&Hl(()=>x.isReadableStream(new Response("").body)),fs={stream:mr&&(e=>e.body)};ks&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!fs[t]&&(fs[t]=x.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new ne(`Response type '${t}' is not supported`,ne.ERR_NOT_SUPPORT,s)})})})(new Response);const Qd=async e=>{if(e==null)return 0;if(x.isBlob(e))return e.size;if(x.isSpecCompliantForm(e))return(await new Request(Ae.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(x.isArrayBufferView(e)||x.isArrayBuffer(e))return e.byteLength;if(x.isURLSearchParams(e)&&(e=e+""),x.isString(e))return(await Xd(e)).byteLength},ep=async(e,t)=>{const n=x.toFiniteNumber(e.getContentLength());return n??Qd(t)},tp=ks&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:h}=Ll(e);u=u?(u+"").toLowerCase():"text";let p=Wd([r,o&&o.toAbortSignal()],i),y;const w=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let _;try{if(a&&Yd&&n!=="get"&&n!=="head"&&(_=await ep(c,s))!==0){let Y=new Request(t,{method:"POST",body:s,duplex:"half"}),le;if(x.isFormData(s)&&(le=Y.headers.get("content-type"))&&c.setContentType(le),Y.body){const[oe,q]=qo(_,us(zo(a)));s=Wo(Y.body,Jo,oe,q)}}x.isString(f)||(f=f?"include":"omit");const T="credentials"in Request.prototype;y=new Request(t,{...h,signal:p,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:T?f:void 0});let O=await fetch(y);const I=mr&&(u==="stream"||u==="response");if(mr&&(l||I&&w)){const Y={};["status","statusText","headers"].forEach(v=>{Y[v]=O[v]});const le=x.toFiniteNumber(O.headers.get("content-length")),[oe,q]=l&&qo(le,us(zo(l),!0))||[];O=new Response(Wo(O.body,Jo,oe,()=>{q&&q(),w&&w()}),Y)}u=u||"text";let L=await fs[x.findKey(fs,u)||"text"](O,e);return!I&&w&&w(),await new Promise((Y,le)=>{Ml(Y,le,{data:L,headers:Ue.from(O.headers),status:O.status,statusText:O.statusText,config:e,request:y})})}catch(T){throw w&&w(),T&&T.name==="TypeError"&&/Load failed|fetch/i.test(T.message)?Object.assign(new ne("Network Error",ne.ERR_NETWORK,e,y),{cause:T.cause||T}):ne.from(T,T&&T.code,e,y)}}),gr={http:gd,xhr:Kd,fetch:tp};x.forEach(gr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Go=e=>`- ${e}`,np=e=>x.isFunction(e)||e===null||e===!1,Ul={getAdapter:e=>{e=x.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!np(n)&&(s=gr[(i=String(n)).toLowerCase()],s===void 0))throw new ne(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Go).join(`
`):" "+Go(o[0]):"as no adapter specified";throw new ne("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:gr};function Zs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new fn(null,e)}function Zo(e){return Zs(e),e.headers=Ue.from(e.headers),e.data=Gs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ul.getAdapter(e.adapter||jn.adapter)(e).then(function(s){return Zs(e),s.data=Gs.call(e,e.transformResponse,s),s.headers=Ue.from(s.headers),s},function(s){return $l(s)||(Zs(e),s&&s.response&&(s.response.data=Gs.call(e,e.transformResponse,s.response),s.response.headers=Ue.from(s.response.headers))),Promise.reject(s)})}const Dl="1.9.0",$s={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{$s[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Xo={};$s.transitional=function(t,n,s){function r(o,i){return"[Axios v"+Dl+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,l)=>{if(t===!1)throw new ne(r(i," has been removed"+(n?" in "+n:"")),ne.ERR_DEPRECATED);return n&&!Xo[i]&&(Xo[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};$s.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function sp(e,t,n){if(typeof e!="object")throw new ne("options must be an object",ne.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new ne("option "+o+" must be "+a,ne.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new ne("Unknown option "+o,ne.ERR_BAD_OPTION)}}const Qn={assertOptions:sp,validators:$s},it=Qn.validators;let Ht=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Bo,response:new Bo}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Dt(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&Qn.assertOptions(s,{silentJSONParsing:it.transitional(it.boolean),forcedJSONParsing:it.transitional(it.boolean),clarifyTimeoutError:it.transitional(it.boolean)},!1),r!=null&&(x.isFunction(r)?n.paramsSerializer={serialize:r}:Qn.assertOptions(r,{encode:it.function,serialize:it.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Qn.assertOptions(n,{baseUrl:it.spelling("baseURL"),withXsrfToken:it.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&x.merge(o.common,o[n.method]);o&&x.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=Ue.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(n)===!1||(a=a&&w.synchronous,l.unshift(w.fulfilled,w.rejected))});const u=[];this.interceptors.response.forEach(function(w){u.push(w.fulfilled,w.rejected)});let c,f=0,h;if(!a){const y=[Zo.bind(this),void 0];for(y.unshift.apply(y,l),y.push.apply(y,u),h=y.length,c=Promise.resolve(n);f<h;)c=c.then(y[f++],y[f++]);return c}h=l.length;let p=n;for(f=0;f<h;){const y=l[f++],w=l[f++];try{p=y(p)}catch(_){w.call(this,_);break}}try{c=Zo.call(this,p)}catch(y){return Promise.reject(y)}for(f=0,h=u.length;f<h;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=Dt(this.defaults,t);const n=Il(t.baseURL,t.url,t.allowAbsoluteUrls);return Ol(n,t.params,t.paramsSerializer)}};x.forEach(["delete","get","head","options"],function(t){Ht.prototype[t]=function(n,s){return this.request(Dt(s||{},{method:t,url:n,data:(s||{}).data}))}});x.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,l){return this.request(Dt(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Ht.prototype[t]=n(),Ht.prototype[t+"Form"]=n(!0)});let rp=class jl{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{s.subscribe(l),o=l}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,l){s.reason||(s.reason=new fn(o,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new jl(function(r){t=r}),cancel:t}}};function op(e){return function(n){return e.apply(null,n)}}function ip(e){return x.isObject(e)&&e.isAxiosError===!0}const yr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(yr).forEach(([e,t])=>{yr[t]=e});function Bl(e){const t=new Ht(e),n=bl(Ht.prototype.request,t);return x.extend(n,Ht.prototype,t,{allOwnKeys:!0}),x.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Bl(Dt(e,r))},n}const _e=Bl(jn);_e.Axios=Ht;_e.CanceledError=fn;_e.CancelToken=rp;_e.isCancel=$l;_e.VERSION=Dl;_e.toFormData=Fs;_e.AxiosError=ne;_e.Cancel=_e.CanceledError;_e.all=function(t){return Promise.all(t)};_e.spread=op;_e.isAxiosError=ip;_e.mergeConfig=Dt;_e.AxiosHeaders=Ue;_e.formToJSON=e=>kl(x.isHTMLForm(e)?new FormData(e):e);_e.getAdapter=Ul.getAdapter;_e.HttpStatusCode=yr;_e.default=_e;const{Axios:T1,AxiosError:P1,CanceledError:O1,isCancel:F1,CancelToken:k1,VERSION:$1,all:M1,Cancel:I1,isAxiosError:L1,spread:N1,toFormData:H1,AxiosHeaders:U1,HttpStatusCode:D1,formToJSON:j1,getAdapter:B1,mergeConfig:V1}=_e,Vl={API_BASE_URL:"https://api.niswey.net/rmscrmseries/"},lp=Vl.API_BASE_URL,Tt=_e.create({baseURL:lp,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});Tt.interceptors.request.use(e=>{var t;return console.log("Axios Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url,e.baseURL+e.url),e},e=>(console.error("Axios Request Error:",e),Promise.reject(e)));Tt.interceptors.response.use(e=>e,e=>{var t,n;return console.error("Axios Response Error:",(t=e.response)==null?void 0:t.status,((n=e.response)==null?void 0:n.data)||e.message),Promise.reject(e)});function Ms(){return{apiClient:Tt,get:(e,t={})=>Tt.get(e,t),post:(e,t={},n={})=>Tt.post(e,t,n),put:(e,t={},n={})=>Tt.put(e,t,n),delete:(e,t={})=>Tt.delete(e,t),patch:(e,t={},n={})=>Tt.patch(e,t,n)}}const ap={class:"min-w-80 max-w-md w-full bg-white shadow-lg rounded-lg ring-1 ring-black ring-opacity-5 overflow-hidden"},cp={class:"p-4"},up={class:"flex items-start"},fp={class:"flex-shrink-0"},dp={key:0,class:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},pp={key:1,class:"h-6 w-6 text-red-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},hp={key:2,class:"h-6 w-6 text-yellow-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},mp={key:3,class:"h-6 w-6 text-blue-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},gp={class:"ml-3 w-0 flex-1 pt-0.5"},yp={class:"text-sm font-medium text-gray-900"},bp={key:0,class:"mt-1 text-sm text-gray-500"},vp={key:0,class:"h-1 bg-gray-200"},xp={__name:"Toast",props:{type:{type:String,default:"info",validator:e=>["success","error","warning","info"].includes(e)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:5e3},autoClose:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:t}){const n=e,s=t,r=Z(!1),o=Z(100);let i=null,l=null;const a=()=>{r.value=!0,n.autoClose&&n.duration>0&&c()},u=()=>{r.value=!1,f(),setTimeout(()=>{s("close")},300)},c=()=>{f(),i=setTimeout(()=>{u()},n.duration);const h=100,y=100/(n.duration/h);l=setInterval(()=>{o.value-=y,o.value<=0&&clearInterval(l)},h)},f=()=>{i&&(clearTimeout(i),i=null),l&&(clearInterval(l),l=null)};return qt(()=>{a()}),Fr(()=>{f()}),(h,p)=>r.value?(V(),J("div",{key:0,class:Te(["transition-all duration-300 ease-in-out transform pointer-events-auto",r.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"])},[g("div",ap,[g("div",cp,[g("div",up,[g("div",fp,[e.type==="success"?(V(),J("svg",dp,p[0]||(p[0]=[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):e.type==="error"?(V(),J("svg",pp,p[1]||(p[1]=[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):e.type==="warning"?(V(),J("svg",hp,p[2]||(p[2]=[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)]))):(V(),J("svg",mp,p[3]||(p[3]=[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),g("div",gp,[g("p",yp,ae(e.title),1),e.message?(V(),J("p",bp,ae(e.message),1)):Oe("",!0)]),g("div",{class:"ml-4 flex-shrink-0 flex"},[g("button",{onClick:u,class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},p[4]||(p[4]=[g("span",{class:"sr-only"},"Close",-1),g("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[g("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))])])]),e.autoClose&&e.duration>0?(V(),J("div",vp,[g("div",{class:Te(["h-full transition-all duration-100 ease-linear",{"bg-green-500":e.type==="success","bg-red-500":e.type==="error","bg-yellow-500":e.type==="warning","bg-blue-500":e.type==="info"}]),style:ys({width:o.value+"%"})},null,6)])):Oe("",!0)])],2)):Oe("",!0)}},wp={class:"fixed top-4 right-4 z-50 space-y-3 pointer-events-none"},Is={__name:"ToastContainer",setup(e,{expose:t}){const n=Z([]);let s=1;const r=l=>{const a={id:s++,type:l.type||"info",title:l.title,message:l.message||"",duration:l.duration||5e3,autoClose:l.autoClose!==!1};return n.value.push(a),n.value.length>5&&n.value.shift(),a.id},o=l=>{const a=n.value.findIndex(u=>u.id===l);a>-1&&n.value.splice(a,1)};return t({addToast:r,removeToast:o,clearAll:()=>{n.value=[]},success:(l,a,u={})=>r({...u,type:"success",title:l,message:a}),error:(l,a,u={})=>r({...u,type:"error",title:l,message:a}),warning:(l,a,u={})=>r({...u,type:"warning",title:l,message:a}),info:(l,a,u={})=>r({...u,type:"info",title:l,message:a})}),(l,a)=>(V(),J("div",wp,[(V(!0),J(xe,null,Ye(n.value,u=>(V(),Ut(xp,{key:u.id,type:u.type,title:u.title,message:u.message,duration:u.duration,"auto-close":u.autoClose,onClose:c=>o(u.id)},null,8,["type","title","message","duration","auto-close","onClose"]))),128))]))}},Jt=Z(null);function jt(){const e=l=>{Jt.value=l,console.log("Toast container set:",l)},t=l=>{if(console.log("Adding toast:",l,"Container:",Jt.value),!Jt.value){console.warn("Toast container not initialized, trying to show toast anyway");const a=`${l.title}: ${l.message}`;alert(a);return}return Jt.value.addToast(l)};return{setToastContainer:e,success:(l,a="",u={})=>t({...u,type:"success",title:l,message:a}),error:(l,a="",u={})=>t({...u,type:"error",title:l,message:a}),warning:(l,a="",u={})=>t({...u,type:"warning",title:l,message:a}),info:(l,a="",u={})=>t({...u,type:"info",title:l,message:a}),clearAll:()=>{Jt.value&&Jt.value.clearAll()}}}const _p={class:"flex flex-col min-h-screen bg-gray-50"},Cp={class:"flex-1 flex flex-col overflow-hidden bg-white"},Sp={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},Rp={key:0,class:"flex items-center justify-center py-16"},Ep={key:1,class:"flex items-center justify-center py-16"},Ap={class:"col-span-4 flex items-center"},Tp={class:"min-w-0 flex-1"},Pp={class:"text-sm font-medium text-gray-900 truncate"},Op={class:"text-xs text-gray-500 truncate"},Fp={class:"col-span-4 flex items-center"},kp={class:"min-w-0 flex-1"},$p={class:"text-sm text-gray-900 truncate"},Mp={class:"col-span-4 flex items-center"},Ip=["onUpdate:modelValue"],Lp={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},Np={class:"flex justify-end mt-6 px-6 py-4"},Hp={class:"flex items-center space-x-4"},Up=["disabled"],Dp={__name:"StepUserMapping",emits:["next","back"],setup(e,{emit:t}){const n=Nn([]),s=Z(!0),{get:r,post:o}=Ms(),i=Z(null),l=jt(),a=t,u=ve(()=>n.filter(p=>p.status&&p.status!=="").length),c=ve(()=>n.length>0&&n.every(p=>p.status&&p.status!=="")),f=async()=>{s.value=!0;const p=new URLSearchParams(window.location.search).get("portal_id");if(!p){l.error("Missing Portal ID","Portal ID is required in the URL to proceed."),s.value=!1;return}try{const{data:y}=await r(`api/hubspot/users?portal_id=${p}`);y.ok&&Array.isArray(y.data)?n.splice(0,n.length,...y.data):console.error("Unexpected response format:",y)}catch(y){console.error("Error fetching users:",y),l.error("Fetch Failed","Failed to fetch users. Please try again.")}finally{s.value=!1}};qt(async()=>{await Vt(),console.log("StepUserMapping mounted, toastContainer.value:",i.value),i.value?(l.setToastContainer(i.value),console.log("Toast container initialized successfully")):console.error("Toast container not found!"),f()});const h=async()=>{if(console.log("validateAndContinue called, allUsersMapped:",c.value),!c.value){console.log("Showing warning toast.."),l.warning("Incomplete Mapping","Please select a user status for all users before proceeding.");return}try{const p=new URLSearchParams(window.location.search).get("portal_id");await o("api/hubspot/users/save",{users:n,portal_id:p}),console.log("Showing success toast..."),l.success("User Mappings Saved",`Successfully saved ${u.value} user mapping(s). Proceeding to next step.`),setTimeout(()=>{a("next")},1500)}catch(p){console.error("Error saving users:",p),console.log("Showing error toast..."),l.error("Save Failed","Failed to save user mappings. Please try again.")}};return(p,y)=>(V(),J("div",_p,[y[5]||(y[5]=g("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[g("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"User Mapping"),g("p",{class:"text-sm text-gray-600 leading-relaxed"},[Me(" RMS app integration will be available only for mapped HubSpot-RMS user accounts. "),g("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),g("div",Cp,[y[3]||(y[3]=We('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-4">HubSpot Member</div><div class="col-span-4">Email ID</div><div class="col-span-4">User Status</div></div></div>',1)),g("div",Sp,[s.value?(V(),J("div",Rp,y[0]||(y[0]=[We('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot Users...</p></div>',1)]))):!s.value&&(!n||n.length===0)?(V(),J("div",Ep,y[1]||(y[1]=[We('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No Users found from hubspot.</p></div>',1)]))):Oe("",!0),(V(!0),J(xe,null,Ye(n,(w,_)=>(V(),J("div",{key:_,class:Te(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":_===n.length-1}])},[g("div",Ap,[g("div",Tp,[g("div",Pp,ae(w.name),1),g("div",Op,ae(w.firstName)+" "+ae(w.lastName),1)])]),g("div",Fp,[g("div",kp,[g("div",$p,ae(w.email),1)])]),g("div",Mp,[Nt(g("select",{"onUpdate:modelValue":T=>n[_].status=T,class:"w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150"},y[2]||(y[2]=[g("option",{disabled:"",value:""},"Select user type...",-1),g("option",{value:"Admin"},"Admin",-1),g("option",{value:"User"},"User",-1)]),8,Ip),[[ln,n[_].status]])])],2))),128))])]),g("div",Lp,[g("div",Np,[g("div",Hp,[g("button",{class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",onClick:h,disabled:!c.value},y[4]||(y[4]=[Me(" Next "),g("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,Up)])])]),me(Is,{ref_key:"toastContainer",ref:i},null,512)]))}},jp={key:0,class:"fixed inset-0 bg-black/30 flex items-center justify-center z-50"},Bp={class:"bg-white rounded-xl p-6 w-full max-w-md shadow-lg relative"},Vp={class:"mb-4"},qp={class:"mb-6"},zp=["value"],Kp={class:"mb-6"},Wp=["value"],Jp={key:0,class:"mb-4 p-3 rounded-lg"},Gp={key:0,class:"text-green-800 bg-green-100 border border-green-200 rounded-lg p-3"},Zp={class:"text-sm mt-1"},Xp={key:1,class:"text-red-800 bg-red-100 border border-red-200 rounded-lg p-3"},Yp={class:"text-sm mt-1"},Qp={class:"flex justify-end space-x-3"},eh=["disabled"],Dr={__name:"AddFieldModal",props:{show:Boolean,rmsOptions:{type:Array,default:()=>[]},hubspotOptions:{type:Array,default:()=>[]}},emits:["close","add"],setup(e,{emit:t}){const n=e,s=t,r=Z(""),o=Z(""),i=Z("");Cn(()=>n.show,f=>{f&&(r.value="",o.value="",i.value="")});const l=ve(()=>{if(!o.value||!i.value)return!1;const f=o.value.field_type,h=i.value.type;return h==="number"?["integer","double","float","number"].includes(f):h==="bool"?["boolean","bool"].includes(f):h==="date"||h==="datetime"?["date","datetime"].includes(f):h==="string"?["string","text"].includes(f):f===h}),a=()=>{r.value="",o.value="",i.value=""},u=()=>{a(),s("close")},c=()=>{if(r.value&&o.value&&i.value){if(!l.value)return;s("add",{hubspotField:r.value,rmsField:o.value,hubspotFieldType:i.value}),u()}else return};return(f,h)=>e.show?(V(),J("div",jp,[g("div",Bp,[g("button",{onClick:u,class:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl"}," × "),h[11]||(h[11]=g("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Add HubSpot field",-1)),g("div",Vp,[h[3]||(h[3]=g("label",{class:"block text-sm text-gray-700 mb-1"},"HubSpot fields",-1)),Nt(g("input",{type:"text","onUpdate:modelValue":h[0]||(h[0]=p=>r.value=p),placeholder:"Field of study",class:"w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[ru,r.value]])]),g("div",qp,[h[5]||(h[5]=g("label",{class:"block text-sm text-gray-700 mb-1"},"Hubspot field Type",-1)),Nt(g("select",{"onUpdate:modelValue":h[1]||(h[1]=p=>i.value=p),class:"w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[h[4]||(h[4]=g("option",{disabled:"",value:""},"Select field type",-1)),(V(!0),J(xe,null,Ye(e.hubspotOptions,p=>(V(),J("option",{key:p,value:p},ae(p.label),9,zp))),128))],512),[[ln,i.value]])]),g("div",Kp,[h[7]||(h[7]=g("label",{class:"block text-sm text-gray-700 mb-1"},"RMS fields",-1)),Nt(g("select",{"onUpdate:modelValue":h[2]||(h[2]=p=>o.value=p),class:"w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[h[6]||(h[6]=g("option",{disabled:"",value:""},"Select field",-1)),(V(!0),J(xe,null,Ye(e.rmsOptions,p=>(V(),J("option",{key:p.id,value:p},ae(p.field_name)+" ("+ae(p.field_type||p.type)+")",9,Wp))),128))],512),[[ln,o.value]])]),o.value&&i.value?(V(),J("div",Jp,[l.value?(V(),J("div",Gp,[h[8]||(h[8]=g("div",{class:"flex items-center"},[g("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[g("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),g("span",{class:"font-medium"},"Field types match!")],-1)),g("p",Zp,"RMS: "+ae(o.value.field_type||o.value.type)+" ↔ HubSpot: "+ae(i.value.type),1)])):(V(),J("div",Xp,[h[9]||(h[9]=g("div",{class:"flex items-center"},[g("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[g("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})]),g("span",{class:"font-medium"},"Field types don't match!")],-1)),g("p",Yp,"RMS: "+ae(o.value.field_type||o.value.type)+" ≠ HubSpot: "+ae(i.value.type),1),h[10]||(h[10]=g("p",{class:"text-sm mt-1"},"Please select fields with matching types.",-1))]))])):Oe("",!0),g("div",Qp,[g("button",{onClick:u,class:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100"}," Cancel "),g("button",{onClick:c,disabled:!l.value||!r.value||!o.value||!i.value,class:Te(["px-4 py-2 rounded-lg text-white transition-colors duration-150",{"bg-blue-600 hover:bg-blue-700 cursor-pointer":l.value&&r.value&&o.value&&i.value,"bg-gray-400 cursor-not-allowed":!l.value||!r.value||!o.value||!i.value}])}," Add ",10,eh)])])])):Oe("",!0)}},th={key:0,class:"fixed inset-0 bg-black/30 flex items-center justify-center z-50"},nh={class:"bg-white rounded-lg p-6 w-full max-w-md relative shadow-lg"},sh={class:"text-sm text-gray-700 mb-6"},rh={class:"font-medium"},jr={__name:"ConfirmRemoveModal",props:{show:Boolean,type:{type:String,required:!0},fieldName:{type:String,required:!0}},emits:["close","confirm"],setup(e,{emit:t}){const n=t,s=()=>n("close"),r=()=>n("confirm");return(o,i)=>e.show?(V(),J("div",th,[g("div",nh,[g("button",{onClick:s,class:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl font-bold"}," × "),i[1]||(i[1]=g("h2",{class:"text-lg font-semibold text-gray-900 mb-2"}," Remove from mappings ",-1)),g("p",sh,[i[0]||(i[0]=Me(" You are about to remove “")),g("span",rh,ae(e.fieldName),1),Me("” field from "+ae(e.type)+" field mappings. ",1)]),g("div",{class:"flex justify-end space-x-4"},[g("button",{onClick:s,class:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100"}," Cancel "),g("button",{onClick:r,class:"bg-red-100 text-red-600 hover:bg-red-200 font-medium px-4 py-2 rounded-lg"}," Remove ")])])])):Oe("",!0)}},oh={class:"flex flex-col min-h-screen bg-gray-50"},ih={class:"flex-1 flex flex-col overflow-hidden bg-white"},lh={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},ah={key:0,class:"flex items-center justify-center py-16"},ch={key:1,class:"flex items-center justify-center py-16"},uh={class:"col-span-5 flex items-center"},fh={class:"min-w-0 flex-1"},dh={class:"text-sm font-medium text-gray-900 truncate"},ph={class:"text-xs text-gray-500 truncate"},hh={class:"col-span-5 flex items-center"},mh=["onUpdate:modelValue"],gh=["value"],yh={class:"col-span-2 flex items-center justify-center"},bh=["onClick"],vh={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},xh={class:"flex justify-between mt-6 px-6 py-4"},wh={class:"flex space-x-4"},_h=["disabled"],Ch={__name:"StepCompanyField",emits:["next","back"],setup(e,{emit:t}){const n=t,{get:s,post:r}=Ms(),o=Z(null),{success:i,error:l,warning:a}=jt(),u=Z(!1),c=Z(!1),f=Z([]),h=Z([]),p=Z([]),y=Z([]),w=Z(null),_=Z(null),T=Z(!0),O=ve(()=>Array.isArray(y.value)?y.value.filter(v=>v.field&&v.field!=="").length:0),I=ve(()=>{if(!Array.isArray(p.value)||!Array.isArray(y.value))return[];const v=y.value.filter(C=>C.field&&C.field!=="").map(C=>C.field.id);return p.value.filter(C=>!v.includes(C.id))}),L=v=>{if(!v||!v.type||!Array.isArray(p.value))return[];const C=y.value.filter(k=>k.field&&k.field!==""&&k.name!==v.name).map(k=>k.field.id);return p.value.filter(k=>{if(C.includes(k.id))return!1;const F=k.field_type,D=v.type;return D==="number"?["integer","double","float","number"].includes(F):D==="bool"?["boolean","bool"].includes(F):D==="date"||D==="datetime"?["date","datetime"].includes(F):D==="string"?["string","text"].includes(F):F===D})};qt(async()=>{if(await Vt(),o.value){const{setToastContainer:v}=jt();v(o.value)}try{T.value=!0;const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required in the URL to proceed."),T.value=!1;return}const[C,k,F]=await Promise.all([s(`api/hubspot/company-fields?portal_id=${v}`),s("api/company/rms-fields"),s(`api/company/field-mapping?portal_id=${v}`)]);f.value=C.data.data||[],p.value=k.data.data||[];const D=F.data.data||[];if(Array.isArray(f.value)&&f.value.length>0){const B=new Map;f.value.forEach(K=>{if(K.fieldType&&K.type){const se=`${K.fieldType}-${K.type}`;B.has(se)||B.set(se,{fieldType:K.fieldType,type:K.type,label:`${K.fieldType} (${K.type})`})}}),h.value=Array.from(B.values())}if(Array.isArray(f.value)&&f.value.length>0){const B=f.value.filter(K=>K.readOnlyValue===!1);y.value=B.map(K=>{const se=D.find(re=>re.hubspot_field===K.name);let ee="";return se&&(ee=p.value.find(re=>re.field_name===se.rms_field)||""),{name:K.name,label:K.label,type:K.type,fieldType:K.fieldType,field:ee}})}else y.value=[]}catch(v){f.value=[],p.value=[],y.value=[],v("Failed to Load Data","Unable to load field data. Please refresh the page and try again.")}finally{T.value=!1}}),console.log("Company",y);const Y=(v,C)=>{w.value=v,_.value=C,c.value=!0},le=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to remove fields.");return}const C={portal_id:parseInt(v),name:w.value.name},k=await r("api/hubspot/remove-company-fields",C);k.data.ok?(y.value.splice(_.value,1),i("Field Removed",`HubSpot field "${w.value.label}" has been removed successfully.`)):l("Removal Failed",k.data.error||"Failed to remove HubSpot field. Please try again.")}catch(v){v("Removal Error","An error occurred while removing the HubSpot field. Please try again.")}c.value=!1,w.value=null,_.value=null},oe=async v=>{try{const C=new URLSearchParams(window.location.search).get("portal_id");if(!C){l("Missing Portal ID","Portal ID is required to create fields.");return}const k=v.hubspotField.toLowerCase().replace(/\s+/g,"_"),F=v.rmsField.field_name,D=v.hubspotFieldType||{},B=D.fieldType||"text",K=D.type||"string",se={portal_id:parseInt(C),rms_field:F,data:{name:k,label:v.hubspotField,groupName:"companyinformation",type:K,fieldType:B,formField:!0}},ee=await r("api/hubspot/add-company-fields",se);ee.data.ok?(y.value.push({name:k,label:v.hubspotField,type:K,fieldType:B,field:v.rmsField||""}),i("Field Created",`HubSpot field "${v.hubspotField}" has been created successfully!`)):l("Creation Failed",ee.data.error||"Failed to create HubSpot field. Please try again.")}catch(C){C("Creation Error","An error occurred while creating the HubSpot field. Please try again.")}u.value=!1},q=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to save field mappings.");return}const C=y.value.filter(D=>D.field&&D.field!=="");if(C.length===0){a("No Fields Mapped","Please map at least one field before proceeding to the next step.");return}const k={portal_id:parseInt(v),mappings:C.map(D=>({hubspot_field:D.name,rms_field:D.field.field_name}))},F=await r("api/company/fields-mapping",k);F.data.ok?(i("Mappings Saved",`Successfully saved ${C.length} company field mapping(s). Proceeding to next step.`),setTimeout(()=>{n("next")},1500)):l("Save Failed",F.data.error||"Failed to save field mappings. Please try again.")}catch(v){v("Save Error","An error occurred while saving field mappings. Please try again.")}};return(v,C)=>{var k;return V(),J("div",oh,[C[10]||(C[10]=g("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[g("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"Company Field Mapping"),g("p",{class:"text-sm text-gray-600 leading-relaxed"},[Me(" Mapping company data fields ensures that information seamlessly moves between RMS and HubSpot, maintaining accuracy and consistency for your HubSpot companies. "),g("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),g("div",ih,[C[8]||(C[8]=We('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-5">HubSpot Company Field</div><div class="col-span-5">RMS Field</div><div class="col-span-2 text-center">Action</div></div></div>',1)),g("div",lh,[T.value?(V(),J("div",ah,C[4]||(C[4]=[We('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot company fields...</p></div>',1)]))):!T.value&&(!y.value||y.value.length===0)?(V(),J("div",ch,C[5]||(C[5]=[We('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No HubSpot company fields found.</p></div>',1)]))):Oe("",!0),!T.value&&y.value&&y.value.length>0?(V(!0),J(xe,{key:2},Ye(y.value,(F,D)=>(V(),J("div",{key:F.name,class:Te(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":D===y.value.length-1}])},[g("div",uh,[g("div",fh,[g("div",dh,ae(F.label),1),g("div",ph,ae(F.name),1)])]),g("div",hh,[Nt(g("select",{"onUpdate:modelValue":B=>F.field=B,class:Te(["w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150",{"border-green-400 bg-green-50 text-green-800":F.field&&F.field!=="","border-gray-300":!F.field||F.field===""}])},[C[6]||(C[6]=g("option",{value:""},"Select RMS Field...",-1)),(V(!0),J(xe,null,Ye(L(F),B=>(V(),J("option",{key:B.id,value:B},ae(B.field_name)+" ("+ae(B.field_type||B.type)+")",9,gh))),128))],10,mh),[[ln,F.field]])]),g("div",yh,[g("button",{class:"inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:B=>Y(F,D),title:"Remove field mapping"},C[7]||(C[7]=[g("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"currentColor",class:"flex-shrink-0"},[g("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"})],-1)]),8,bh)])],2))),128)):Oe("",!0)])]),g("div",vh,[g("div",xh,[g("div",wh,[g("button",{class:"border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:C[0]||(C[0]=F=>v.$emit("back"))}," Back "),g("button",{class:"border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:C[1]||(C[1]=F=>u.value=!0)}," Add HubSpot field ")]),g("button",{class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",onClick:q,disabled:O.value===0},C[9]||(C[9]=[Me(" Next "),g("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,_h)])]),me(Dr,{show:u.value,rmsOptions:I.value,hubspotOptions:h.value,onClose:C[2]||(C[2]=F=>u.value=!1),onAdd:oe},null,8,["show","rmsOptions","hubspotOptions"]),me(jr,{show:c.value,fieldName:((k=w.value)==null?void 0:k.label)||"",type:"company",onClose:C[3]||(C[3]=F=>c.value=!1),onConfirm:le},null,8,["show","fieldName"]),me(Is,{ref_key:"toastContainer",ref:o},null,512)])}}},Sh={class:"flex flex-col min-h-screen bg-gray-50"},Rh={class:"flex-1 flex flex-col overflow-hidden bg-white"},Eh={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},Ah={key:0,class:"flex items-center justify-center py-16"},Th={key:1,class:"flex items-center justify-center py-16"},Ph={class:"col-span-5 flex items-center"},Oh={class:"min-w-0 flex-1"},Fh={class:"text-sm font-medium text-gray-900 truncate"},kh={class:"text-xs text-gray-500 truncate"},$h={class:"col-span-5 flex items-center"},Mh=["onUpdate:modelValue"],Ih=["value"],Lh={class:"col-span-2 flex items-center justify-center"},Nh=["onClick"],Hh={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},Uh={class:"flex justify-between mt-6 px-6 py-4"},Dh={class:"flex space-x-4"},jh=["disabled"],Bh={__name:"StepContactField",emits:["next","back"],setup(e,{emit:t}){const n=t,{get:s,post:r}=Ms(),o=Z(null),{success:i,error:l,warning:a}=jt(),u=Z(!1),c=Z(!1),f=Z([]),h=Z([]),p=Z([]),y=Z([]),w=Z(null),_=Z(null),T=Z(!0),O=ve(()=>Array.isArray(y.value)?y.value.filter(v=>v.field&&v.field!=="").length:0),I=ve(()=>{if(!Array.isArray(p.value)||!Array.isArray(y.value))return[];const v=y.value.filter(C=>C.field&&C.field!=="").map(C=>C.field.id);return p.value.filter(C=>!v.includes(C.id))}),L=v=>{if(!v||!v.type||!Array.isArray(p.value))return[];const C=y.value.filter(k=>k.field&&k.field!==""&&k.name!==v.name).map(k=>k.field.id);return p.value.filter(k=>{if(C.includes(k.id))return!1;const F=k.field_type,D=v.type;return D==="number"?["integer","double","float","number"].includes(F):D==="bool"?["boolean","bool"].includes(F):D==="date"||D==="datetime"?["date","datetime"].includes(F):D==="string"?["string","text"].includes(F):F===D})};qt(async()=>{if(await Vt(),o.value){const{setToastContainer:v}=jt();v(o.value)}try{T.value=!0;const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required in the URL to proceed."),T.value=!1;return}const[C,k,F]=await Promise.all([s(`api/hubspot/contact-fields?portal_id=${v}`),s("api/contact/rms-fields"),s(`api/contact/field-mapping?portal_id=${v}`)]);f.value=C.data.data||[],p.value=k.data.data||[];const D=F.data.data||[];if(Array.isArray(f.value)&&f.value.length>0){const B=new Map;f.value.forEach(K=>{if(K.fieldType&&K.type){const se=`${K.fieldType}-${K.type}`;B.has(se)||B.set(se,{fieldType:K.fieldType,type:K.type,label:`${K.fieldType} (${K.type})`})}}),h.value=Array.from(B.values())}if(Array.isArray(f.value)&&f.value.length>0){const B=f.value.filter(K=>K.readOnlyValue===!1);y.value=B.map(K=>{const se=D.find(re=>re.hubspot_field===K.name);let ee="";return se&&(ee=p.value.find(re=>re.field_name===se.rms_field)||""),{name:K.name,label:K.label,type:K.type,fieldType:K.fieldType,field:ee}})}else y.value=[]}catch(v){f.value=[],p.value=[],y.value=[],v("Failed to Load Data","Unable to load contact field data. Please refresh the page and try again.")}finally{T.value=!1}});const Y=(v,C)=>{w.value=v,_.value=C,c.value=!0},le=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to remove fields.");return}const C={portal_id:parseInt(v),name:w.value.name},k=await r("api/hubspot/remove-contact-fields",C);k.data.ok?(y.value.splice(_.value,1),i("Field Removed",`HubSpot contact field "${w.value.label}" has been removed successfully.`)):l("Removal Failed",k.data.error||"Failed to remove HubSpot contact field. Please try again.")}catch(v){v("Removal Error","An error occurred while removing the HubSpot contact field. Please try again.")}c.value=!1,w.value=null,_.value=null},oe=async v=>{try{const C=new URLSearchParams(window.location.search).get("portal_id");if(!C){l("Missing Portal ID","Portal ID is required to create fields.");return}const k=v.hubspotField.toLowerCase().replace(/\s+/g,"_"),F=v.rmsField.field_name,D=v.hubspotFieldType||{},B=D.fieldType||"text",K=D.type||"string",se={portal_id:parseInt(C),rms_field:F,data:{name:k,label:v.hubspotField,groupName:"contactinformation",type:K,fieldType:B,formField:!0}},ee=await r("api/hubspot/add-contact-fields",se);ee.data.ok?(y.value.push({name:k,label:v.hubspotField,type:K,fieldType:B,field:v.rmsField||""}),i("Field Created",`HubSpot contact field "${v.hubspotField}" has been created successfully!`)):l("Creation Failed",ee.data.error||"Failed to create HubSpot contact field. Please try again.")}catch(C){C("Creation Error","An error occurred while creating the HubSpot contact field. Please try again.")}u.value=!1},q=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to save field mappings.");return}const C=y.value.filter(D=>D.field&&D.field!=="");if(C.length===0){a("No Fields Mapped","Please map at least one field before proceeding to the next step.");return}const k={portal_id:parseInt(v),mappings:C.map(D=>({hubspot_field:D.name,rms_field:D.field.field_name}))},F=await r("api/contact/fields-mapping",k);F.data.ok?(i("Mappings Saved",`Successfully saved ${C.length} contact field mapping(s). Proceeding to next step.`),setTimeout(()=>{n("next")},1500)):l("Save Failed",F.data.error||"Failed to save field mappings. Please try again.")}catch(v){v("Save Error","An error occurred while saving field mappings. Please try again.")}};return(v,C)=>{var k;return V(),J("div",Sh,[C[10]||(C[10]=g("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[g("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"Contact Field Mapping"),g("p",{class:"text-sm text-gray-600 leading-relaxed"},[Me(" Mapping contact data fields ensures that information seamlessly moves between RMS and HubSpot, maintaining accuracy and consistency for your HubSpot contacts. "),g("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),g("div",Rh,[C[8]||(C[8]=We('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-5">HubSpot Contact Field</div><div class="col-span-5">RMS Field</div><div class="col-span-2 text-center">Action</div></div></div>',1)),g("div",Eh,[T.value?(V(),J("div",Ah,C[4]||(C[4]=[We('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot contact fields...</p></div>',1)]))):!T.value&&(!y.value||y.value.length===0)?(V(),J("div",Th,C[5]||(C[5]=[We('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No HubSpot contact fields found.</p></div>',1)]))):Oe("",!0),!T.value&&y.value&&y.value.length>0?(V(!0),J(xe,{key:2},Ye(y.value,(F,D)=>(V(),J("div",{key:F.name,class:Te(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":D===y.value.length-1}])},[g("div",Ph,[g("div",Oh,[g("div",Fh,ae(F.label),1),g("div",kh,ae(F.name),1)])]),g("div",$h,[Nt(g("select",{"onUpdate:modelValue":B=>F.field=B,class:Te(["w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150",{"border-green-400 bg-green-50 text-green-800":F.field&&F.field!=="","border-gray-300":!F.field||F.field===""}])},[C[6]||(C[6]=g("option",{value:""},"Select RMS Field...",-1)),(V(!0),J(xe,null,Ye(L(F),B=>(V(),J("option",{key:B.id,value:B},ae(B.field_name)+" ("+ae(B.field_type||B.type)+")",9,Ih))),128))],10,Mh),[[ln,F.field]])]),g("div",Lh,[g("button",{class:"inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:B=>Y(F,D),title:"Remove field mapping"},C[7]||(C[7]=[g("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"currentColor",class:"flex-shrink-0"},[g("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"})],-1)]),8,Nh)])],2))),128)):Oe("",!0)])]),g("div",Hh,[g("div",Uh,[g("div",Dh,[g("button",{class:"border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:C[0]||(C[0]=F=>v.$emit("back"))}," Back "),g("button",{class:"border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:C[1]||(C[1]=F=>u.value=!0)}," Add HubSpot field ")]),g("button",{class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",onClick:q,disabled:O.value===0},C[9]||(C[9]=[Me(" Next "),g("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,jh)])]),me(Dr,{show:u.value,rmsOptions:I.value,hubspotOptions:h.value,onClose:C[2]||(C[2]=F=>u.value=!1),onAdd:oe},null,8,["show","rmsOptions","hubspotOptions"]),me(jr,{show:c.value,fieldName:((k=w.value)==null?void 0:k.label)||"",type:"contact",onClose:C[3]||(C[3]=F=>c.value=!1),onConfirm:le},null,8,["show","fieldName"]),me(Is,{ref_key:"toastContainer",ref:o},null,512)])}}},Vh={class:"flex flex-col min-h-screen bg-gray-50"},qh={class:"flex-1 flex flex-col overflow-hidden bg-white"},zh={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},Kh={key:0,class:"flex items-center justify-center py-16"},Wh={key:1,class:"flex items-center justify-center py-16"},Jh={class:"col-span-5 flex items-center"},Gh={class:"min-w-0 flex-1"},Zh={class:"text-sm font-medium text-gray-900 truncate"},Xh={class:"text-xs text-gray-500 truncate"},Yh={class:"col-span-5 flex items-center"},Qh=["onUpdate:modelValue"],e1=["value"],t1={class:"col-span-2 flex items-center justify-center"},n1=["onClick"],s1={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},r1={class:"flex justify-between mt-6 px-6 py-4"},o1={class:"flex space-x-4"},i1=["disabled"],l1={__name:"StepDealField",emits:["next","back"],setup(e,{emit:t}){const{get:n,post:s}=Ms(),r=Z(null),{success:o,error:i,warning:l}=jt(),a=Z(!1),u=Z(!1),c=Z([]),f=Z([]),h=Z([]),p=Z([]),y=Z(null),w=Z(null),_=Z(!0),T=ve(()=>Array.isArray(p.value)?p.value.filter(q=>q.field&&q.field!=="").length:0),O=ve(()=>{if(!Array.isArray(h.value)||!Array.isArray(p.value))return[];const q=p.value.filter(v=>v.field&&v.field!=="").map(v=>v.field.id);return h.value.filter(v=>!q.includes(v.id))}),I=q=>{if(!q||!q.type||!Array.isArray(h.value))return[];const v=p.value.filter(C=>C.field&&C.field!==""&&C.name!==q.name).map(C=>C.field.id);return h.value.filter(C=>{if(v.includes(C.id))return!1;const k=C.field_type,F=q.type;return F==="number"?["integer","double","float","number"].includes(k):F==="bool"?["boolean","bool"].includes(k):F==="date"||F==="datetime"?["date","datetime"].includes(k):F==="string"?["string","text"].includes(k):k===F})},L=(q,v)=>{y.value=q,w.value=v,u.value=!0},Y=async()=>{try{const q=new URLSearchParams(window.location.search).get("portal_id");if(!q){i("Missing Portal ID","Portal ID is required to remove fields.");return}const v={portal_id:parseInt(q),name:y.value.name},C=await s("api/hubspot/remove-deal-fields",v);C.data.ok?(p.value.splice(w.value,1),o("Field Removed",`HubSpot deal field "${y.value.label}" has been removed successfully.`)):i("Removal Failed",C.data.error||"Failed to remove HubSpot deal field. Please try again.")}catch(q){q("Removal Error","An error occurred while removing the HubSpot deal field. Please try again.")}u.value=!1,y.value=null,w.value=null};qt(async()=>{if(await Vt(),r.value){const{setToastContainer:q}=jt();q(r.value)}try{_.value=!0;const q=new URLSearchParams(window.location.search).get("portal_id");if(!q){i("Missing Portal ID","Portal ID is required in the URL to proceed."),_.value=!1;return}const[v,C,k]=await Promise.all([n(`api/hubspot/deal-fields?portal_id=${q}`),n("api/deal/rms-fields"),n(`api/deal/field-mapping?portal_id=${q}`)]);c.value=v.data.data||[],h.value=C.data.data||[];const F=k.data.data||[];if(Array.isArray(c.value)&&c.value.length>0){const D=new Map;c.value.forEach(B=>{if(B.fieldType&&B.type){const K=`${B.fieldType}-${B.type}`;D.has(K)||D.set(K,{fieldType:B.fieldType,type:B.type,label:`${B.fieldType} (${B.type})`})}}),f.value=Array.from(D.values())}if(Array.isArray(c.value)&&c.value.length>0){const D=c.value.filter(B=>B.readOnlyValue===!1);p.value=D.map(B=>{const K=F.find(ee=>ee.hubspot_field===B.name);let se="";return K&&(se=h.value.find(ee=>ee.field_name===K.rms_field)||""),{name:B.name,label:B.label,type:B.type,fieldType:B.fieldType,field:se}})}else p.value=[]}catch(q){c.value=[],h.value=[],p.value=[],q("Failed to Load Data","Unable to load deal field data. Please refresh the page and try again.")}finally{_.value=!1}}),console.log("deals",p);const le=async q=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){i("Missing Portal ID","Portal ID is required to create fields.");return}const C=q.hubspotField.toLowerCase().replace(/\s+/g,"_"),k=q.rmsField.field_name,F=q.hubspotFieldType||{},D=F.fieldType||"text",B=F.type||"string",K={portal_id:parseInt(v),rms_field:k,data:{name:C,label:q.hubspotField,groupName:"dealinformation",type:B,fieldType:D,formField:!0}},se=await s("api/hubspot/add-deal-fields",K);se.data.ok?(p.value.push({name:C,label:q.hubspotField,type:B,fieldType:D,field:q.rmsField||""}),o("Field Created",`HubSpot deal field "${q.hubspotField}" has been created successfully!`)):i("Creation Failed",se.data.error||"Failed to create HubSpot deal field. Please try again.")}catch(v){v("Creation Error","An error occurred while creating the HubSpot deal field. Please try again.")}a.value=!1},oe=async()=>{try{const q=new URLSearchParams(window.location.search).get("portal_id");if(!q){i("Missing Portal ID","Portal ID is required to save field mappings.");return}const v=p.value.filter(F=>F.field&&F.field!=="");if(v.length===0){l("No Fields Mapped","Please map at least one field before finishing the setup.");return}const C={portal_id:parseInt(q),mappings:v.map(F=>({hubspot_field:F.name,rms_field:F.field.field_name}))},k=await s("api/deal/fields-mapping",C);k.data.ok?o("Setup Complete!",`Successfully saved ${v.length} deal field mapping(s). Your HubSpot integration setup is now complete!`):i("Save Failed",k.data.error||"Failed to save deal field mappings. Please try again.")}catch(q){q("Save Error","An error occurred while saving deal field mappings. Please try again.")}};return(q,v)=>{var C;return V(),J("div",Vh,[v[9]||(v[9]=g("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[g("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"Deal Field Mapping"),g("p",{class:"text-sm text-gray-600 leading-relaxed"},[Me(" Mapping Deal data fields ensures that information seamlessly moves between RMS and HubSpot, maintaining accuracy and consistency for your HubSpot deal. "),g("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),g("div",qh,[v[8]||(v[8]=We('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-5">HubSpot Deal Field</div><div class="col-span-5">RMS Field</div><div class="col-span-2 text-center">Action</div></div></div>',1)),g("div",zh,[_.value?(V(),J("div",Kh,v[4]||(v[4]=[We('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot deal fields...</p></div>',1)]))):!_.value&&(!p.value||p.value.length===0)?(V(),J("div",Wh,v[5]||(v[5]=[We('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No HubSpot deal fields found.</p></div>',1)]))):Oe("",!0),!_.value&&p.value&&p.value.length>0?(V(!0),J(xe,{key:2},Ye(p.value,(k,F)=>(V(),J("div",{key:k.name,class:Te(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":F===p.value.length-1}])},[g("div",Jh,[g("div",Gh,[g("div",Zh,ae(k.label),1),g("div",Xh,ae(k.name),1)])]),g("div",Yh,[Nt(g("select",{"onUpdate:modelValue":D=>k.field=D,class:Te(["w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150",{"border-green-400 bg-green-50 text-green-800":k.field&&k.field!=="","border-gray-300":!k.field||k.field===""}])},[v[6]||(v[6]=g("option",{value:""},"Select RMS Field...",-1)),(V(!0),J(xe,null,Ye(I(k),D=>(V(),J("option",{key:D.id,value:D},ae(D.field_name)+" ("+ae(D.field_type||D.type)+")",9,e1))),128))],10,Qh),[[ln,k.field]])]),g("div",t1,[g("button",{class:"inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:D=>L(k,F),title:"Remove field mapping"},v[7]||(v[7]=[g("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"currentColor",class:"flex-shrink-0"},[g("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"})],-1)]),8,n1)])],2))),128)):Oe("",!0)])]),g("div",s1,[g("div",r1,[g("div",o1,[g("button",{class:"border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:v[0]||(v[0]=k=>q.$emit("back"))}," Back "),g("button",{class:"border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:v[1]||(v[1]=k=>a.value=!0)}," Add HubSpot field ")]),g("button",{class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",onClick:oe,disabled:T.value===0}," Finish ",8,i1)])]),me(Dr,{show:a.value,hubspotOptions:f.value,rmsOptions:O.value,onClose:v[2]||(v[2]=k=>a.value=!1),onAdd:le},null,8,["show","hubspotOptions","rmsOptions"]),me(jr,{show:u.value,fieldName:((C=y.value)==null?void 0:C.label)||"",type:"deal",onClose:v[3]||(v[3]=k=>u.value=!1),onConfirm:Y},null,8,["show","fieldName"]),me(Is,{ref_key:"toastContainer",ref:r},null,512)])}}},Xs=Vl.API_BASE_URL;function a1(){const e=Z(!1),t=Z(null),n=i=>{if(i.startsWith("http"))return i;const l=i.startsWith("/")?i.slice(1):i;return`${Xs.endsWith("/")?Xs:`${Xs}/`}${l}`},s=async(i,l={})=>{e.value=!0,t.value=null;try{const a=n(i);console.log("API Call:",a);const u=await fetch(a,{headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest",...l.headers},...l}),c=await u.json();if(!u.ok)throw new Error(c.message||`HTTP error! status: ${u.status}`);return c}catch(a){throw t.value=a.message,console.error("API Error:",a),a}finally{e.value=!1}};return{loading:e,error:t,get:async(i,l={})=>{try{const a=new URLSearchParams(l),u=a.toString()?`${i}?${a}`:i,c=await s(u,{method:"GET"});return{ok:!0,data:c.data||c,hasAccess:c.hasAccess||!1}}catch(a){return{ok:!1,error:a.message}}},post:(i,l={})=>s(i,{method:"POST",body:JSON.stringify(l)}),apiCall:s}}const ht=Z(null),Gt=Z(!1),St=Z(!1),Ys=Z(!1),yn=Z(null);function c1(){const e=mf(),{get:t}=a1(),n=ve(()=>{var c;return((c=ht.value)==null?void 0:c.role)==="Admin"}),s=ve(()=>Gt.value&&St.value),r=()=>{const c=new URLSearchParams(window.location.search);return{portal_id:c.get("portal_id"),user_id:c.get("user_id"),email:c.get("email")}},o=async()=>{Ys.value=!0,yn.value=null;try{const c=r();if(!c.portal_id||!c.user_id)throw new Error("Missing required authentication parameters");console.log("Checking user access with params:",{portal_id:c.portal_id,user_id:c.user_id?"present":"missing",email:c.email});const f=await t("api/user",{portal_id:c.portal_id,user_id:c.user_id});if(f.ok)return ht.value=f.data,Gt.value=!0,St.value=f.hasAccess,console.log("User authentication result:",{email:ht.value.email,role:ht.value.role,hasAccess:St.value}),{success:!0,user:ht.value,hasAccess:St.value};throw new Error(f.error||"Authentication failed")}catch(c){return console.error("Authentication error:",c),yn.value=c.message,ht.value=null,Gt.value=!1,St.value=!1,{success:!1,error:c.message}}finally{Ys.value=!1}},i=()=>{e.push("/error?message=Access denied. Admin role required to access the wizard.")},l=()=>{e.push("/auth")};return{user:ht,isAuthenticated:Gt,hasAdminAccess:St,authLoading:Ys,authError:yn,isAdmin:n,canAccessWizard:s,checkUserAccess:o,redirectToAccessDenied:i,redirectToAuth:l,logout:()=>{ht.value=null,Gt.value=!1,St.value=!1,yn.value=null,l()},clearAuth:()=>{ht.value=null,Gt.value=!1,St.value=!1,yn.value=null},getUrlParams:r}}const u1={key:0,class:"p-6 max-w-5xl mx-auto"},f1={key:1,class:"p-6 max-w-5xl mx-auto"},d1={class:"text-center"},p1={class:"bg-red-50 border border-red-200 rounded-lg p-6"},h1={class:"text-red-700 mb-4"},m1={key:0},g1={key:0,class:"text-sm text-red-600 mb-4"},y1={key:2,class:"p-6 max-w-5xl w-full mx-auto"},b1={class:"flex space-x-8"},v1={class:"w-1/5 relative"},x1={class:"text-sm font-medium relative"},w1={class:"flex flex-col items-center z-0 mt-1"},_1={class:"flex-1 bg-white rounded-lg shadow p-6 transition-all duration-300"},C1={__name:"Wizard",setup(e){const{user:t,authLoading:n,authError:s,canAccessWizard:r,checkUserAccess:o,redirectToAuth:i}=c1();qt(async()=>{console.log("Wizard component mounted, checking user access...");const w=await o();w.success?w.hasAccess?console.log("User has admin access, proceeding with wizard"):console.warn("User authenticated but lacks admin access"):console.error("Authentication failed:",w.error)});const l=()=>{i()},a=[{label:"Authorization"},{label:"User mapping",component:Dp},{label:"Company field mapping",component:Ch},{label:"Contact field mapping",component:Bh},{label:"Deal field mapping",component:l1}],u=Z(1),c=ve(()=>u.value===a.length-1),f=Z({users:[],company:{},contact:{},deal:{}}),h=()=>{u.value<a.length-1&&u.value++},p=()=>{u.value>0&&u.value--},y=()=>{console.log("Final Submission:",f.value)};return(w,_)=>(V(),Ut(Of,null,{default:Ti(()=>[Ve(n)?(V(),J("div",u1,_[1]||(_[1]=[g("div",{class:"text-center"},[g("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),g("p",{class:"text-gray-600"},"Checking access permissions...")],-1)]))):!Ve(r)&&!Ve(n)?(V(),J("div",f1,[g("div",d1,[g("div",p1,[_[4]||(_[4]=g("div",{class:"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full"},[g("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[g("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),_[5]||(_[5]=g("h3",{class:"text-lg font-semibold text-red-800 mb-2"},"Access Denied",-1)),g("p",h1,[_[3]||(_[3]=Me(" You need Admin role to access the wizard. ")),Ve(t)?(V(),J("span",m1,[_[2]||(_[2]=Me("Your current role is: ")),g("strong",null,ae(Ve(t).role||"Not assigned"),1)])):Oe("",!0)]),Ve(s)?(V(),J("p",g1,ae(Ve(s)),1)):Oe("",!0),g("button",{onClick:l,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition"}," Go to Authorization ")])])])):(V(),J("div",y1,[_[6]||(_[6]=g("div",{class:"text-center mb-8"},[g("img",{src:yf,alt:"Niswey HubSpot Integration",class:"mx-auto h-12 mb-2"}),g("h2",{class:"text-xl font-semibold text-gray-700"},[Me(" Connecting "),g("span",{class:"font-bold text-gray-600"},"RMS"),Me(" to "),g("span",{class:"text-gray-600"},"HubSpot")])],-1)),g("div",b1,[g("div",v1,[g("ul",x1,[(V(),J(xe,null,Ye(a,(T,O)=>g("li",{key:O,class:"relative flex space-x-2"},[g("div",w1,[g("div",{class:Te(["w-3 h-3 rounded-full",[u.value>=O?"bg-blue-600":"bg-gray-300"]])},null,2),O<a.length-1?(V(),J("div",{key:0,class:Te(["h-8 w-1",u.value>O?"bg-blue-600":"bg-gray-300"])},null,2)):Oe("",!0)]),g("div",{class:Te(u.value===O?"text-blue-600 font-semibold":"text-gray-500")},ae(T.label),3)])),64))])]),g("div",_1,[(V(),Ut(Ja(a[u.value].component),{modelValue:f.value[a[u.value].key],"onUpdate:modelValue":_[0]||(_[0]=T=>f.value[a[u.value].key]=T),onNext:h,onBack:p,"is-final":c.value,onSubmit:y},null,40,["modelValue","is-final"]))])])]))]),_:1}))}},S1=[{path:"/",redirect:"/wizard"},{path:"/wizard",component:C1,meta:{requiresAuth:!0,requiresAdmin:!0}}],R1=pf({history:ju(),routes:S1});lu(uu).use(R1).mount("#app");
