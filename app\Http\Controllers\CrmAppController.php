<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class CrmAppController extends Controller
{
    public function initiate(Request $request)
    {
        $input = $request->input();
        $fetchDestHeader = $request->header('sec-fetch-dest');
        if ($fetchDestHeader != 'iframe' && env('APP_ENV') == 'DEV') {
            return view('error', ['message' => 'Please open this from your HubSpot CRM Card']);
        }

        $html = file_get_contents(public_path().'/crmbuild/index.html');

        return response($html, 200)->header('Content-Type', 'text/html');
    }
}
