<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Illuminate\Http\Request;
use App\Helpers\Hubspot;
use App\Models\{CrmContactFieldMapping,CrmContactField};

class ContactController extends Controller
{

     public function getContactFields(Request $request)
    {
        try {
            $input = $request->input();
            //Get Contact Property
            $response = Hubspot::getContactFields($input['portal_id']);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to fetch contact fields from HubSpot'
                ]);
            }

            return $this->jsonOk(['data' => $response]);
        } catch (Exception $e) {
            Log::error('[ContactController:getContactFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching contact fields'
            ]);
        }
    }

    public function getRmsFields(){
        $getRmsFields = CrmContactField::all();
        return $this->jsonOk(['data' => $getRmsFields]);
    }

    public function addContactFields(Request $request){
        try {
            
            $this->validate($request, [
                'portal_id' => 'required',
                'data' => 'required|array',
                'rms_field' => 'required',
            ]);

            $input = $request->input();
            //Get Contact Property
            $response = Hubspot::addHubspotProperty($input['portal_id'],'contacts',$input['data']);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to add contact fields from HubSpot'
                ]);
            }

             // Save Contact field Mapping
            CrmContactFieldMapping::updateOrCreate(
                [
                    'portal_id' => $input['portal_id'],
                    'hubspot_field' => $input['data']['name'],
                ],
                [
                    'rms_field' => $input['rms_field'],
                ]
            );

            return $this->jsonOk(['data' => json_decode($response, true)]);
        }   catch (Exception $e) {
            Log::error('[ContactController:addContactFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while adding contact fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function removeContactFields(Request $request){
        try {
            
            $this->validate($request, [
                'portal_id' => 'required',
                'name' => 'required'
            ]);

            $input = $request->input();
            //Get Contact Property
            // $response = Hubspot::deleteHubspotProperty($input['portal_id'],'contacts',$input['name']);

            // if (isset($response->status) && $response->status == 'error') {
            //     return $this->jsonError([
            //         'error' => $response->message
            //     ]);
            // }
            
            //Remove From Contact Field Mapping Table
            
            CrmContactFieldMapping::where('portal_id', $input['portal_id'])
                ->where('hubspot_field', $input['name'])
                ->delete();
    
            return $this->jsonOk(['message' => 'Contact field remove successfully.']);
        }   catch (Exception $e) {
            Log::error('[ContactController:deleteContactFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while deleting contact fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveContactFieldsMapping(Request $request){
        try {
            $this->validate($request, [
                'portal_id' => 'required',
                'mappings' => 'required|array',
            ]);

            $input = $request->input();

            $submittedHubspotFields = collect($input['mappings'])->pluck('hubspot_field')->toArray();

            // delete mappings that are not submitted
            
            CrmContactFieldMapping::where('portal_id', $input['portal_id'])
                ->whereNotIn('hubspot_field', $submittedHubspotFields)
                ->delete();

            //Save Contact Field Mapping
            foreach ($input['mappings'] as $value) {
                CrmContactFieldMapping::updateOrCreate(
                    ['portal_id' => $input['portal_id'], 'hubspot_field' => $value['hubspot_field']],
                    ['rms_field' => $value['rms_field']]
                );
            }

            return $this->jsonOk(['message' => 'Contact field mapping saved successfully.']);
        }   catch (Exception $e) {
            Log::error('[ContactController:saveContactFieldsMapping] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while saving contact field mapping',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getContactFieldMapping(Request $request){
        try {
            $input = $request->input();
            $getContactFieldMapping = CrmContactFieldMapping::where('portal_id', $input['portal_id'])->get();
            return $this->jsonOk(['data' => $getContactFieldMapping]);
        }   catch (Exception $e) {
            Log::error('[ContactController:getContactFieldMapping] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching contact field mapping',
                'message' => $e->getMessage()
            ]);
        }
    }
 
}
