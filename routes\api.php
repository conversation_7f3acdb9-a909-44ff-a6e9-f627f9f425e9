<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DealController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\HubSpotController;
use App\Helpers\Func;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});
Route::get('hubspot/crm', [HubSpotController::class, 'crm']);
// Route::get('/hubspot/crm', function (Request $request) {
//    //$phone = Func::parsePhone($request->query('phone'));
//    $response = [
//     "results" => [
//         [
//             "objectId" => 245,
//             "title" => "API-22: APIs working too fast",
//             "link" => "http://example.com/1",
//             "created" => "2016-09-15",
//             "priority" => "HIGH",
//             "project" => "API",
//             "description" => "Customer reported that the APIs are just running too fast. This is causing a problem in that they're so happy.",
//             "reporter_type" => "Account Manager",
//             "status" => "In Progress",
//             "ticket_type" => "Bug",
//             "updated" => "2016-09-28",
//             "actions" => [
//                 [
//                     "type" => "IFRAME",
//                     "width" => 890,
//                     "height" => 748,
//                     "uri" => "https://example.com/edit-iframe-contents",
//                     "label" => "Edit",
//                     "associatedObjectProperties" => []
//                 ],
//                 [
//                     "type" => "IFRAME",
//                     "width" => 890,
//                     "height" => 748,
//                     "uri" => "https://example.com/reassign-iframe-contents",
//                     "label" => "Reassign",
//                     "associatedObjectProperties" => []
//                 ],
//                 [
//                     "type" => "ACTION_HOOK",
//                     "httpMethod" => "PUT",
//                     "associatedObjectProperties" => [],
//                     "uri" => "https://example.com/tickets/245/resolve",
//                     "label" => "Resolve"
//                 ],
//                 [
//                     "type" => "CONFIRMATION_ACTION_HOOK",
//                     "confirmationMessage" => "Are you sure you want to delete this ticket?",
//                     "confirmButtonText" => "Yes",
//                     "cancelButtonText" => "No",
//                     "httpMethod" => "DELETE",
//                     "associatedObjectProperties" => ["protected_account"],
//                     "uri" => "https://example.com/tickets/245",
//                     "label" => "Delete"
//                 ]
//             ]
//         ],
//         [
//             "objectId" => 988,
//             "title" => "API-54: Question about bulk APIs",
//             "link" => "http://example.com/2",
//             "created" => "2016-08-04",
//             "priority" => "HIGH",
//             "project" => "API",
//             "reported_by" => "<EMAIL>",
//             "description" => "Customer is not able to find documentation about our bulk Contacts APIs.",
//             "reporter_type" => "Support Rep",
//             "status" => "Resolved",
//             "ticket_type" => "Bug",
//             "updated" => "2016-09-23",
//             "properties" => [
//                 [
//                     "label" => "Resolved by",
//                     "dataType" => "EMAIL",
//                     "value" => "<EMAIL>"
//                 ],
//                 [
//                     "label" => "Resolution type",
//                     "dataType" => "STRING",
//                     "value" => "Referred to documentation"
//                 ],
//                 [
//                     "label" => "Resolution impact",
//                     "dataType" => "CURRENCY",
//                     "value" => "94.34",
//                     "currencyCode" => "GBP"
//                 ]
//             ],
//             "actions" => [
//                 [
//                     "type" => "IFRAME",
//                     "width" => 890,
//                     "height" => 748,
//                     "uri" => "https://example.com/edit-iframe-contents",
//                     "label" => "Edit"
//                 ],
//                 [
//                     "type" => "CONFIRMATION_ACTION_HOOK",
//                     "confirmationMessage" => "Are you sure you want to delete this ticket?",
//                     "confirmButtonText" => "Yes",
//                     "cancelButtonText" => "No",
//                     "httpMethod" => "DELETE",
//                     "associatedObjectProperties" => ["protected_account"],
//                     "uri" => "https://example.com/tickets/245",
//                     "label" => "Delete"
//                 ]
//             ]
//         ]
//     ],
//     "settingsAction" => [
//         "type" => "IFRAME",
//         "width" => 890,
//         "height" => 748,
//         "uri" => "https://example.com/settings-iframe-contents",
//         "label" => "Settings"
//     ],
//     "primaryAction" => [
//         "type" => "IFRAME",
//         "width" => 890,
//         "height" => 748,
//         "uri" => "https://example.com/create-iframe-contents",
//         "label" => "Create Ticket"
//     ]
// ];
// return response()->json($response);
// });
//     return response()->json([
//          "results" => [
//          [
//             "type" => "info",
//             "title" => "RMS Enrichment",
//             "properties" => [
//                "label" => "phone",
//                "value" => $phone
//             ],
//             [
//                "label" => "email",
//                "value" => $request->query('email')
//             ]
//          ]
//       ],
//         "actions" => [
//                [
//                   "type" => "IFRAME",
//                   "width" => 600,
//                   "height" => 400,
//                   "uri" => "https://yourdomain.com/vue-popup",
//                   "label" => "Open Settings"
//                ],
//                ],
//     ], 200, ['Content-Type' => 'application/json']);
// });

//Users

Route::get('/hubspot/users', [UserController::class, 'getUsers']);
Route::get('/user', [UserController::class, 'getUsersDetail']);
Route::post('/hubspot/users/save', [UserController::class, 'saveUsers']);


//Company

Route::get('/hubspot/company-fields', [CompanyController::class, 'getCompanyFields']);
Route::get('/company/rms-fields', [CompanyController::class, 'getRmsFields']);
Route::post('/hubspot/add-company-fields', [CompanyController::class, 'addCompanyFields']);
Route::post('/hubspot/remove-company-fields', [CompanyController::class, 'removeCompanyFields']);
Route::post('/company/fields-mapping', [CompanyController::class, 'saveCompanyFieldsMapping']);
Route::get('/company/field-mapping', [CompanyController::class, 'getCompanyFieldMapping']);

//Contact

Route::get('/hubspot/contact-fields', [ContactController::class, 'getContactFields']);
Route::get('/contact/rms-fields', [ContactController::class, 'getRmsFields']);
Route::post('/hubspot/add-contact-fields', [ContactController::class, 'addContactFields']);
Route::post('/hubspot/remove-contact-fields', [ContactController::class, 'removeContactFields']);
Route::post('/contact/fields-mapping', [ContactController::class, 'saveContactFieldsMapping']);
Route::get('/contact/field-mapping', [ContactController::class, 'getContactFieldMapping']);

//Deal

Route::get('/hubspot/deal-fields', [DealController::class, 'getDealFields']);
Route::get('/deal/rms-fields', [DealController::class, 'getRmsFields']);
Route::post('/hubspot/add-deal-fields', [DealController::class, 'addDealFields']);
Route::post('/hubspot/remove-deal-fields', [DealController::class, 'removeDealFields']);
Route::post('/deal/fields-mapping', [DealController::class, 'saveDealFieldsMapping']);
Route::get('/deal/field-mapping', [DealController::class, 'getDealFieldMapping']);