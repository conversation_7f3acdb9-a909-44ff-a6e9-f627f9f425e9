<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Illuminate\Http\Request;
use App\Helpers\Hubspot;
use App\Models\{CrmDealFieldMapping,CrmDealField};

class DealController extends Controller
{

     public function getDealFields(Request $request)
    {
        try {
            $input = $request->input();
            //Get Deal Property
            $response = Hubspot::getDealFields($input['portal_id']);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to fetch deal fields from HubSpot'
                ]);
            }

            return $this->jsonOk(['data' => $response]);
        } catch (Exception $e) {
            Log::error('[DealController:getDealFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching deal fields'
            ]);
        }
    }

    public function getRmsFields(){
        $getRmsFields = CrmDealField::all();
        return $this->jsonOk(['data' => $getRmsFields]);
    }

    public function addDealFields(Request $request){
        try {
            $this->validate($request, [
                'portal_id' => 'required',
                'data' => 'required|array',
                'rms_field' => 'required',
            ]);
            $input = $request->input();
            //Get Deal Property
            $response = Hubspot::addHubspotProperty($input['portal_id'],'deals',$input['data']);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to add deal fields from HubSpot'
                ]);
            }

            // Save Deal field Mapping
            CrmDealFieldMapping::updateOrCreate(
                [
                    'portal_id' => $input['portal_id'],
                    'hubspot_field' => $input['data']['name'],
                ],
                [
                    'rms_field' => $input['rms_field'],
                ]
            );

            return $this->jsonOk(['data' => json_decode($response, true)]);
        } catch (Exception $e) {
            Log::error('[DealController:addDealFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while adding deal fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function removeDealFields(Request $request){
        try {
            $this->validate($request, [
                'portal_id' => 'required',
                'name' => 'required'
            ]);
            $input = $request->input();
            //Get Deal Property
            // $response = Hubspot::deleteHubspotProperty($input['portal_id'],'deals',$input['name']);

            // if (isset($response->status) && $response->status == 'error') {
            //     return $this->jsonError([
            //         'error' => $response->message
            //     ]);
            // }
            
            //Remove From Deal Field Mapping Table
            
            CrmDealFieldMapping::where('portal_id', $input['portal_id'])
                ->where('hubspot_field', $input['name'])
                ->delete();

            return $this->jsonOk(['message' => 'Deal field remove successfully.']);
        }   catch (Exception $e) {
            Log::error('[DealController:deleteDealFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while deleting deal fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveDealFieldsMapping(Request $request){
        try {
            $this->validate($request, [
                'portal_id' => 'required',
                'mappings' => 'required|array',
            ]);

            $input = $request->input();

            $submittedHubspotFields = collect($input['mappings'])->pluck('hubspot_field')->toArray();

            // delete mappings that are not submitted
            
            CrmDealFieldMapping::where('portal_id', $input['portal_id'])
                ->whereNotIn('hubspot_field', $submittedHubspotFields)
                ->delete();

            //Save Deal Field Mapping
            foreach ($input['mappings'] as $value) {
                CrmDealFieldMapping::updateOrCreate(
                    ['portal_id' => $input['portal_id'], 'hubspot_field' => $value['hubspot_field']],
                    ['rms_field' => $value['rms_field']]
                );
            }

            return $this->jsonOk(['message' => 'Deal field mapping saved successfully.']);
        }   catch (Exception $e) {
            Log::error('[DealController:saveDealFieldsMapping] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while saving deal field mapping',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getDealFieldMapping(Request $request){
        try {
            $input = $request->input();
            $getDealFieldMapping = CrmDealFieldMapping::where('portal_id', $input['portal_id'])->get();
            return $this->jsonOk(['data' => $getDealFieldMapping]);
        }   catch (Exception $e) {
            Log::error('[DealController:getDealFieldMapping] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching deal field mapping',
                'message' => $e->getMessage()
            ]);
        }
    }

}
