(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(r){if(r.ep)return;r.ep=!0;const o=s(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function qn(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const ie={},kt=[],Ye=()=>{},ji=()=>!1,Vs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),zn=e=>e.startsWith("onUpdate:"),Te=Object.assign,Wn=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Bi=Object.prototype.hasOwnProperty,re=(e,t)=>Bi.call(e,t),W=Array.isArray,Lt=e=>ps(e)==="[object Map]",qs=e=>ps(e)==="[object Set]",yr=e=>ps(e)==="[object Date]",G=e=>typeof e=="function",he=e=>typeof e=="string",je=e=>typeof e=="symbol",ce=e=>e!==null&&typeof e=="object",uo=e=>(ce(e)||G(e))&&G(e.then)&&G(e.catch),fo=Object.prototype.toString,ps=e=>fo.call(e),Vi=e=>ps(e).slice(8,-1),po=e=>ps(e)==="[object Object]",Kn=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ss=qn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),zs=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},qi=/-(\w)/g,Ue=zs(e=>e.replace(qi,(t,s)=>s?s.toUpperCase():"")),zi=/\B([A-Z])/g,Et=zs(e=>e.replace(zi,"-$1").toLowerCase()),Ws=zs(e=>e.charAt(0).toUpperCase()+e.slice(1)),hn=zs(e=>e?`on${Ws(e)}`:""),ht=(e,t)=>!Object.is(e,t),Cs=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ho=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Ps=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let br;const Ks=()=>br||(br=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Js(e){if(W(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],r=he(n)?Zi(n):Js(n);if(r)for(const o in r)t[o]=r[o]}return t}else if(he(e)||ce(e))return e}const Wi=/;(?![^(]*\))/g,Ki=/:([^]+)/,Ji=/\/\*[^]*?\*\//g;function Zi(e){const t={};return e.replace(Ji,"").split(Wi).forEach(s=>{if(s){const n=s.split(Ki);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function _e(e){let t="";if(he(e))t=e;else if(W(e))for(let s=0;s<e.length;s++){const n=_e(e[s]);n&&(t+=n+" ")}else if(ce(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Gi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Xi=qn(Gi);function mo(e){return!!e||e===""}function Yi(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=Zs(e[n],t[n]);return s}function Zs(e,t){if(e===t)return!0;let s=yr(e),n=yr(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=je(e),n=je(t),s||n)return e===t;if(s=W(e),n=W(t),s||n)return s&&n?Yi(e,t):!1;if(s=ce(e),n=ce(t),s||n){if(!s||!n)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Zs(e[i],t[i]))return!1}}return String(e)===String(t)}function Qi(e,t){return e.findIndex(s=>Zs(s,t))}const go=e=>!!(e&&e.__v_isRef===!0),se=e=>he(e)?e:e==null?"":W(e)||ce(e)&&(e.toString===fo||!G(e.toString))?go(e)?se(e.value):JSON.stringify(e,yo,2):String(e),yo=(e,t)=>go(t)?yo(e,t.value):Lt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,r],o)=>(s[mn(n,o)+" =>"]=r,s),{})}:qs(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>mn(s))}:je(t)?mn(t):ce(t)&&!W(t)&&!po(t)?String(t):t,mn=(e,t="")=>{var s;return je(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Fe;class el{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Fe,!t&&Fe&&(this.index=(Fe.scopes||(Fe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=Fe;try{return Fe=this,t()}finally{Fe=s}}}on(){++this._on===1&&(this.prevScope=Fe,Fe=this)}off(){this._on>0&&--this._on===0&&(Fe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function tl(){return Fe}let ae;const gn=new WeakSet;class bo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Fe&&Fe.active&&Fe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,gn.has(this)&&(gn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||xo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,vr(this),_o(this);const t=ae,s=De;ae=this,De=!0;try{return this.fn()}finally{wo(this),ae=t,De=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Gn(t);this.deps=this.depsTail=void 0,vr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?gn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){En(this)&&this.run()}get dirty(){return En(this)}}let vo=0,ns,rs;function xo(e,t=!1){if(e.flags|=8,t){e.next=rs,rs=e;return}e.next=ns,ns=e}function Jn(){vo++}function Zn(){if(--vo>0)return;if(rs){let t=rs;for(rs=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;ns;){let t=ns;for(ns=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function _o(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function wo(e){let t,s=e.depsTail,n=s;for(;n;){const r=n.prevDep;n.version===-1?(n===s&&(s=r),Gn(n),sl(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=r}e.deps=t,e.depsTail=s}function En(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Co(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Co(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ls)||(e.globalVersion=ls,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!En(e))))return;e.flags|=2;const t=e.dep,s=ae,n=De;ae=e,De=!0;try{_o(e);const r=e.fn(e._value);(t.version===0||ht(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ae=s,De=n,wo(e),e.flags&=-3}}function Gn(e,t=!1){const{dep:s,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)Gn(o,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function sl(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let De=!0;const So=[];function ot(){So.push(De),De=!1}function it(){const e=So.pop();De=e===void 0?!0:e}function vr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ae;ae=void 0;try{t()}finally{ae=s}}}let ls=0;class nl{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Xn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!De||ae===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ae)s=this.activeLink=new nl(ae,this),ae.deps?(s.prevDep=ae.depsTail,ae.depsTail.nextDep=s,ae.depsTail=s):ae.deps=ae.depsTail=s,Ao(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=ae.depsTail,s.nextDep=void 0,ae.depsTail.nextDep=s,ae.depsTail=s,ae.deps===s&&(ae.deps=n)}return s}trigger(t){this.version++,ls++,this.notify(t)}notify(t){Jn();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Zn()}}}function Ao(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Ao(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Fn=new WeakMap,Ct=Symbol(""),On=Symbol(""),as=Symbol("");function be(e,t,s){if(De&&ae){let n=Fn.get(e);n||Fn.set(e,n=new Map);let r=n.get(s);r||(n.set(s,r=new Xn),r.map=n,r.key=s),r.track()}}function nt(e,t,s,n,r,o){const i=Fn.get(e);if(!i){ls++;return}const l=a=>{a&&a.trigger()};if(Jn(),t==="clear")i.forEach(l);else{const a=W(e),u=a&&Kn(s);if(a&&s==="length"){const c=Number(n);i.forEach((d,y)=>{(y==="length"||y===as||!je(y)&&y>=c)&&l(d)})}else switch((s!==void 0||i.has(void 0))&&l(i.get(s)),u&&l(i.get(as)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Ct)),Lt(e)&&l(i.get(On)));break;case"delete":a||(l(i.get(Ct)),Lt(e)&&l(i.get(On)));break;case"set":Lt(e)&&l(i.get(Ct));break}}Zn()}function Ot(e){const t=ne(e);return t===e?t:(be(t,"iterate",as),Ne(e)?t:t.map(ge))}function Gs(e){return be(e=ne(e),"iterate",as),e}const rl={__proto__:null,[Symbol.iterator](){return yn(this,Symbol.iterator,ge)},concat(...e){return Ot(this).concat(...e.map(t=>W(t)?Ot(t):t))},entries(){return yn(this,"entries",e=>(e[1]=ge(e[1]),e))},every(e,t){return et(this,"every",e,t,void 0,arguments)},filter(e,t){return et(this,"filter",e,t,s=>s.map(ge),arguments)},find(e,t){return et(this,"find",e,t,ge,arguments)},findIndex(e,t){return et(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return et(this,"findLast",e,t,ge,arguments)},findLastIndex(e,t){return et(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return et(this,"forEach",e,t,void 0,arguments)},includes(...e){return bn(this,"includes",e)},indexOf(...e){return bn(this,"indexOf",e)},join(e){return Ot(this).join(e)},lastIndexOf(...e){return bn(this,"lastIndexOf",e)},map(e,t){return et(this,"map",e,t,void 0,arguments)},pop(){return Xt(this,"pop")},push(...e){return Xt(this,"push",e)},reduce(e,...t){return xr(this,"reduce",e,t)},reduceRight(e,...t){return xr(this,"reduceRight",e,t)},shift(){return Xt(this,"shift")},some(e,t){return et(this,"some",e,t,void 0,arguments)},splice(...e){return Xt(this,"splice",e)},toReversed(){return Ot(this).toReversed()},toSorted(e){return Ot(this).toSorted(e)},toSpliced(...e){return Ot(this).toSpliced(...e)},unshift(...e){return Xt(this,"unshift",e)},values(){return yn(this,"values",ge)}};function yn(e,t,s){const n=Gs(e),r=n[t]();return n!==e&&!Ne(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=s(o.value)),o}),r}const ol=Array.prototype;function et(e,t,s,n,r,o){const i=Gs(e),l=i!==e&&!Ne(e),a=i[t];if(a!==ol[t]){const d=a.apply(e,o);return l?ge(d):d}let u=s;i!==e&&(l?u=function(d,y){return s.call(this,ge(d),y,e)}:s.length>2&&(u=function(d,y){return s.call(this,d,y,e)}));const c=a.call(i,u,n);return l&&r?r(c):c}function xr(e,t,s,n){const r=Gs(e);let o=s;return r!==e&&(Ne(e)?s.length>3&&(o=function(i,l,a){return s.call(this,i,l,a,e)}):o=function(i,l,a){return s.call(this,i,ge(l),a,e)}),r[t](o,...n)}function bn(e,t,s){const n=ne(e);be(n,"iterate",as);const r=n[t](...s);return(r===-1||r===!1)&&er(s[0])?(s[0]=ne(s[0]),n[t](...s)):r}function Xt(e,t,s=[]){ot(),Jn();const n=ne(e)[t].apply(e,s);return Zn(),it(),n}const il=qn("__proto__,__v_isRef,__isVue"),Ro=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(je));function ll(e){je(e)||(e=String(e));const t=ne(this);return be(t,"has",e),t.hasOwnProperty(e)}class To{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return o;if(s==="__v_raw")return n===(r?o?yl:Po:o?Oo:Fo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const i=W(t);if(!r){let a;if(i&&(a=rl[s]))return a;if(s==="hasOwnProperty")return ll}const l=Reflect.get(t,s,we(t)?t:n);return(je(s)?Ro.has(s):il(s))||(r||be(t,"get",s),o)?l:we(l)?i&&Kn(s)?l:l.value:ce(l)?r?$o(l):Xs(l):l}}class Eo extends To{constructor(t=!1){super(!1,t)}set(t,s,n,r){let o=t[s];if(!this._isShallow){const a=mt(o);if(!Ne(n)&&!mt(n)&&(o=ne(o),n=ne(n)),!W(t)&&we(o)&&!we(n))return a?!1:(o.value=n,!0)}const i=W(t)&&Kn(s)?Number(s)<t.length:re(t,s),l=Reflect.set(t,s,n,we(t)?t:r);return t===ne(r)&&(i?ht(n,o)&&nt(t,"set",s,n):nt(t,"add",s,n)),l}deleteProperty(t,s){const n=re(t,s);t[s];const r=Reflect.deleteProperty(t,s);return r&&n&&nt(t,"delete",s,void 0),r}has(t,s){const n=Reflect.has(t,s);return(!je(s)||!Ro.has(s))&&be(t,"has",s),n}ownKeys(t){return be(t,"iterate",W(t)?"length":Ct),Reflect.ownKeys(t)}}class al extends To{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const cl=new Eo,ul=new al,fl=new Eo(!0);const Pn=e=>e,xs=e=>Reflect.getPrototypeOf(e);function dl(e,t,s){return function(...n){const r=this.__v_raw,o=ne(r),i=Lt(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=r[e](...n),c=s?Pn:t?$s:ge;return!t&&be(o,"iterate",a?On:Ct),{next(){const{value:d,done:y}=u.next();return y?{value:d,done:y}:{value:l?[c(d[0]),c(d[1])]:c(d),done:y}},[Symbol.iterator](){return this}}}}function _s(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function pl(e,t){const s={get(r){const o=this.__v_raw,i=ne(o),l=ne(r);e||(ht(r,l)&&be(i,"get",r),be(i,"get",l));const{has:a}=xs(i),u=t?Pn:e?$s:ge;if(a.call(i,r))return u(o.get(r));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&be(ne(r),"iterate",Ct),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ne(o),l=ne(r);return e||(ht(r,l)&&be(i,"has",r),be(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=ne(l),u=t?Pn:e?$s:ge;return!e&&be(a,"iterate",Ct),l.forEach((c,d)=>r.call(o,u(c),u(d),i))}};return Te(s,e?{add:_s("add"),set:_s("set"),delete:_s("delete"),clear:_s("clear")}:{add(r){!t&&!Ne(r)&&!mt(r)&&(r=ne(r));const o=ne(this);return xs(o).has.call(o,r)||(o.add(r),nt(o,"add",r,r)),this},set(r,o){!t&&!Ne(o)&&!mt(o)&&(o=ne(o));const i=ne(this),{has:l,get:a}=xs(i);let u=l.call(i,r);u||(r=ne(r),u=l.call(i,r));const c=a.call(i,r);return i.set(r,o),u?ht(o,c)&&nt(i,"set",r,o):nt(i,"add",r,o),this},delete(r){const o=ne(this),{has:i,get:l}=xs(o);let a=i.call(o,r);a||(r=ne(r),a=i.call(o,r)),l&&l.call(o,r);const u=o.delete(r);return a&&nt(o,"delete",r,void 0),u},clear(){const r=ne(this),o=r.size!==0,i=r.clear();return o&&nt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=dl(r,e,t)}),s}function Yn(e,t){const s=pl(e,t);return(n,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(re(s,r)&&r in n?s:n,r,o)}const hl={get:Yn(!1,!1)},ml={get:Yn(!1,!0)},gl={get:Yn(!0,!1)};const Fo=new WeakMap,Oo=new WeakMap,Po=new WeakMap,yl=new WeakMap;function bl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function vl(e){return e.__v_skip||!Object.isExtensible(e)?0:bl(Vi(e))}function Xs(e){return mt(e)?e:Qn(e,!1,cl,hl,Fo)}function xl(e){return Qn(e,!1,fl,ml,Oo)}function $o(e){return Qn(e,!0,ul,gl,Po)}function Qn(e,t,s,n,r){if(!ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=vl(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?n:s);return r.set(e,l),l}function It(e){return mt(e)?It(e.__v_raw):!!(e&&e.__v_isReactive)}function mt(e){return!!(e&&e.__v_isReadonly)}function Ne(e){return!!(e&&e.__v_isShallow)}function er(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function _l(e){return!re(e,"__v_skip")&&Object.isExtensible(e)&&ho(e,"__v_skip",!0),e}const ge=e=>ce(e)?Xs(e):e,$s=e=>ce(e)?$o(e):e;function we(e){return e?e.__v_isRef===!0:!1}function z(e){return wl(e,!1)}function wl(e,t){return we(e)?e:new Cl(e,t)}class Cl{constructor(t,s){this.dep=new Xn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:ne(t),this._value=s?t:ge(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||Ne(t)||mt(t);t=n?t:ne(t),ht(t,s)&&(this._rawValue=t,this._value=n?t:ge(t),this.dep.trigger())}}function ut(e){return we(e)?e.value:e}const Sl={get:(e,t,s)=>t==="__v_raw"?e:ut(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const r=e[t];return we(r)&&!we(s)?(r.value=s,!0):Reflect.set(e,t,s,n)}};function Mo(e){return It(e)?e:new Proxy(e,Sl)}class Al{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Xn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ls-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return xo(this,!0),!0}get value(){const t=this.dep.track();return Co(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Rl(e,t,s=!1){let n,r;return G(e)?n=e:(n=e.get,r=e.set),new Al(n,r,s)}const ws={},Ms=new WeakMap;let xt;function Tl(e,t=!1,s=xt){if(s){let n=Ms.get(s);n||Ms.set(s,n=[]),n.push(e)}}function El(e,t,s=ie){const{immediate:n,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=s,u=I=>r?I:Ne(I)||r===!1||r===0?rt(I,1):rt(I);let c,d,y,m,g=!1,C=!1;if(we(e)?(d=()=>e.value,g=Ne(e)):It(e)?(d=()=>u(e),g=!0):W(e)?(C=!0,g=e.some(I=>It(I)||Ne(I)),d=()=>e.map(I=>{if(we(I))return I.value;if(It(I))return u(I);if(G(I))return a?a(I,2):I()})):G(e)?t?d=a?()=>a(e,2):e:d=()=>{if(y){ot();try{y()}finally{it()}}const I=xt;xt=c;try{return a?a(e,3,[m]):e(m)}finally{xt=I}}:d=Ye,t&&r){const I=d,X=r===!0?1/0:r;d=()=>rt(I(),X)}const w=tl(),k=()=>{c.stop(),w&&w.active&&Wn(w.effects,c)};if(o&&t){const I=t;t=(...X)=>{I(...X),k()}}let D=C?new Array(e.length).fill(ws):ws;const K=I=>{if(!(!(c.flags&1)||!c.dirty&&!I))if(t){const X=c.run();if(r||g||(C?X.some((fe,ue)=>ht(fe,D[ue])):ht(X,D))){y&&y();const fe=xt;xt=c;try{const ue=[X,D===ws?void 0:C&&D[0]===ws?[]:D,m];D=X,a?a(t,3,ue):t(...ue)}finally{xt=fe}}}else c.run()};return l&&l(K),c=new bo(d),c.scheduler=i?()=>i(K,!1):K,m=I=>Tl(I,!1,c),y=c.onStop=()=>{const I=Ms.get(c);if(I){if(a)a(I,4);else for(const X of I)X();Ms.delete(c)}},t?n?K(!0):D=c.run():i?i(K.bind(null,!0),!0):c.run(),k.pause=c.pause.bind(c),k.resume=c.resume.bind(c),k.stop=k,k}function rt(e,t=1/0,s){if(t<=0||!ce(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,we(e))rt(e.value,t,s);else if(W(e))for(let n=0;n<e.length;n++)rt(e[n],t,s);else if(qs(e)||Lt(e))e.forEach(n=>{rt(n,t,s)});else if(po(e)){for(const n in e)rt(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&rt(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function hs(e,t,s,n){try{return n?e(...n):e()}catch(r){Ys(r,t,s)}}function Qe(e,t,s,n){if(G(e)){const r=hs(e,t,s,n);return r&&uo(r)&&r.catch(o=>{Ys(o,t,s)}),r}if(W(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Qe(e[o],t,s,n));return r}}function Ys(e,t,s,n=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ie;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const c=l.ec;if(c){for(let d=0;d<c.length;d++)if(c[d](e,a,u)===!1)return}l=l.parent}if(o){ot(),hs(o,null,10,[e,a,u]),it();return}}Fl(e,s,r,n,i)}function Fl(e,t,s,n=!0,r=!1){if(r)throw e;console.error(e)}const Ae=[];let Ge=-1;const Nt=[];let ft=null,Mt=0;const ko=Promise.resolve();let ks=null;function zt(e){const t=ks||ko;return e?t.then(this?e.bind(this):e):t}function Ol(e){let t=Ge+1,s=Ae.length;for(;t<s;){const n=t+s>>>1,r=Ae[n],o=cs(r);o<e||o===e&&r.flags&2?t=n+1:s=n}return t}function tr(e){if(!(e.flags&1)){const t=cs(e),s=Ae[Ae.length-1];!s||!(e.flags&2)&&t>=cs(s)?Ae.push(e):Ae.splice(Ol(t),0,e),e.flags|=1,Lo()}}function Lo(){ks||(ks=ko.then(No))}function Pl(e){W(e)?Nt.push(...e):ft&&e.id===-1?ft.splice(Mt+1,0,e):e.flags&1||(Nt.push(e),e.flags|=1),Lo()}function _r(e,t,s=Ge+1){for(;s<Ae.length;s++){const n=Ae[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;Ae.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Io(e){if(Nt.length){const t=[...new Set(Nt)].sort((s,n)=>cs(s)-cs(n));if(Nt.length=0,ft){ft.push(...t);return}for(ft=t,Mt=0;Mt<ft.length;Mt++){const s=ft[Mt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}ft=null,Mt=0}}const cs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function No(e){try{for(Ge=0;Ge<Ae.length;Ge++){const t=Ae[Ge];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),hs(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ge<Ae.length;Ge++){const t=Ae[Ge];t&&(t.flags&=-2)}Ge=-1,Ae.length=0,Io(),ks=null,(Ae.length||Nt.length)&&No()}}let ye=null,Ho=null;function Ls(e){const t=ye;return ye=e,Ho=e&&e.type.__scopeId||null,t}function Uo(e,t=ye,s){if(!t||e._n)return e;const n=(...r)=>{n._d&&Pr(-1);const o=Ls(t);let i;try{i=e(...r)}finally{Ls(o),n._d&&Pr(1)}return i};return n._n=!0,n._c=!0,n._d=!0,n}function St(e,t){if(ye===null)return e;const s=sn(ye),n=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=ie]=t[r];o&&(G(o)&&(o={mounted:o,updated:o}),o.deep&&rt(i),n.push({dir:o,instance:s,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function bt(e,t,s,n){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[n];a&&(ot(),Qe(a,s,8,[e.el,l,e,t]),it())}}const $l=Symbol("_vte"),Ml=e=>e.__isTeleport;function sr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,sr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Do(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Is(e,t,s,n,r=!1){if(W(e)){e.forEach((g,C)=>Is(g,t&&(W(t)?t[C]:t),s,n,r));return}if(Ht(n)&&!r){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Is(e,t,s,n.component.subTree);return}const o=n.shapeFlag&4?sn(n.component):n.el,i=r?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===ie?l.refs={}:l.refs,d=l.setupState,y=ne(d),m=d===ie?()=>!1:g=>re(y,g);if(u!=null&&u!==a&&(he(u)?(c[u]=null,m(u)&&(d[u]=null)):we(u)&&(u.value=null)),G(a))hs(a,l,12,[i,c]);else{const g=he(a),C=we(a);if(g||C){const w=()=>{if(e.f){const k=g?m(a)?d[a]:c[a]:a.value;r?W(k)&&Wn(k,o):W(k)?k.includes(o)||k.push(o):g?(c[a]=[o],m(a)&&(d[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else g?(c[a]=i,m(a)&&(d[a]=i)):C&&(a.value=i,e.k&&(c[e.k]=i))};i?(w.id=-1,Le(w,s)):w()}}}Ks().requestIdleCallback;Ks().cancelIdleCallback;const Ht=e=>!!e.type.__asyncLoader,jo=e=>e.type.__isKeepAlive;function kl(e,t){Bo(e,"a",t)}function Ll(e,t){Bo(e,"da",t)}function Bo(e,t,s=ve){const n=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Qs(t,n,s),s){let r=s.parent;for(;r&&r.parent;)jo(r.parent.vnode)&&Il(n,t,s,r),r=r.parent}}function Il(e,t,s,n){const r=Qs(t,e,n,!0);nr(()=>{Wn(n[t],r)},s)}function Qs(e,t,s=ve,n=!1){if(s){const r=s[e]||(s[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ot();const l=ms(s),a=Qe(t,s,e,i);return l(),it(),a});return n?r.unshift(o):r.push(o),o}}const at=e=>(t,s=ve)=>{(!fs||e==="sp")&&Qs(e,(...n)=>t(...n),s)},Nl=at("bm"),Ft=at("m"),Hl=at("bu"),Ul=at("u"),Dl=at("bum"),nr=at("um"),jl=at("sp"),Bl=at("rtg"),Vl=at("rtc");function ql(e,t=ve){Qs("ec",e,t)}const zl="components",Vo=Symbol.for("v-ndc");function Wl(e){return he(e)?Kl(zl,e,!1)||e:e||Vo}function Kl(e,t,s=!0,n=!1){const r=ye||ve;if(r){const o=r.type;{const l=La(o,!1);if(l&&(l===t||l===Ue(t)||l===Ws(Ue(t))))return o}const i=wr(r[e]||o[e],t)||wr(r.appContext[e],t);return!i&&n?o:i}}function wr(e,t){return e&&(e[t]||e[Ue(t)]||e[Ws(Ue(t))])}function Be(e,t,s,n){let r;const o=s,i=W(e);if(i||he(e)){const l=i&&It(e);let a=!1,u=!1;l&&(a=!Ne(e),u=mt(e),e=Gs(e)),r=new Array(e.length);for(let c=0,d=e.length;c<d;c++)r[c]=t(a?u?$s(ge(e[c])):ge(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ce(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];r[a]=t(e[c],c,a,o)}}else r=[];return r}function Jl(e,t,s={},n,r){if(ye.ce||ye.parent&&Ht(ye.parent)&&ye.parent.ce)return N(),Bt(de,null,[pe("slot",s,n)],64);let o=e[t];o&&o._c&&(o._d=!1),N();const i=o&&qo(o(s)),l=s.key||i&&i.key,a=Bt(de,{key:(l&&!je(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),o&&o._c&&(o._d=!0),a}function qo(e){return e.some(t=>ir(t)?!(t.type===lt||t.type===de&&!qo(t.children)):!0)?e:null}const $n=e=>e?ui(e)?sn(e):$n(e.parent):null,os=Te(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>$n(e.parent),$root:e=>$n(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Wo(e),$forceUpdate:e=>e.f||(e.f=()=>{tr(e.update)}),$nextTick:e=>e.n||(e.n=zt.bind(e.proxy)),$watch:e=>ga.bind(e)}),vn=(e,t)=>e!==ie&&!e.__isScriptSetup&&re(e,t),Zl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return n[t];case 2:return r[t];case 4:return s[t];case 3:return o[t]}else{if(vn(n,t))return i[t]=1,n[t];if(r!==ie&&re(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&re(u,t))return i[t]=3,o[t];if(s!==ie&&re(s,t))return i[t]=4,s[t];Mn&&(i[t]=0)}}const c=os[t];let d,y;if(c)return t==="$attrs"&&be(e.attrs,"get",""),c(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(s!==ie&&re(s,t))return i[t]=4,s[t];if(y=a.config.globalProperties,re(y,t))return y[t]},set({_:e},t,s){const{data:n,setupState:r,ctx:o}=e;return vn(r,t)?(r[t]=s,!0):n!==ie&&re(n,t)?(n[t]=s,!0):re(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:r,propsOptions:o}},i){let l;return!!s[i]||e!==ie&&re(e,i)||vn(t,i)||(l=o[0])&&re(l,i)||re(n,i)||re(os,i)||re(r.config.globalProperties,i)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:re(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Cr(e){return W(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Mn=!0;function Gl(e){const t=Wo(e),s=e.proxy,n=e.ctx;Mn=!1,t.beforeCreate&&Sr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:d,mounted:y,beforeUpdate:m,updated:g,activated:C,deactivated:w,beforeDestroy:k,beforeUnmount:D,destroyed:K,unmounted:I,render:X,renderTracked:fe,renderTriggered:ue,errorCaptured:U,serverPrefetch:v,expose:_,inheritAttrs:O,components:E,directives:L,filters:H}=t;if(u&&Xl(u,n,null),i)for(const Y in i){const ee=i[Y];G(ee)&&(n[Y]=ee.bind(s))}if(r){const Y=r.call(s,s);ce(Y)&&(e.data=Xs(Y))}if(Mn=!0,o)for(const Y in o){const ee=o[Y],gt=G(ee)?ee.bind(s,s):G(ee.get)?ee.get.bind(s,s):Ye,bs=!G(ee)&&G(ee.set)?ee.set.bind(s):Ye,yt=$e({get:gt,set:bs});Object.defineProperty(n,Y,{enumerable:!0,configurable:!0,get:()=>yt.value,set:qe=>yt.value=qe})}if(l)for(const Y in l)zo(l[Y],n,s,Y);if(a){const Y=G(a)?a.call(s):a;Reflect.ownKeys(Y).forEach(ee=>{na(ee,Y[ee])})}c&&Sr(c,e,"c");function Q(Y,ee){W(ee)?ee.forEach(gt=>Y(gt.bind(s))):ee&&Y(ee.bind(s))}if(Q(Nl,d),Q(Ft,y),Q(Hl,m),Q(Ul,g),Q(kl,C),Q(Ll,w),Q(ql,U),Q(Vl,fe),Q(Bl,ue),Q(Dl,D),Q(nr,I),Q(jl,v),W(_))if(_.length){const Y=e.exposed||(e.exposed={});_.forEach(ee=>{Object.defineProperty(Y,ee,{get:()=>s[ee],set:gt=>s[ee]=gt})})}else e.exposed||(e.exposed={});X&&e.render===Ye&&(e.render=X),O!=null&&(e.inheritAttrs=O),E&&(e.components=E),L&&(e.directives=L),v&&Do(e)}function Xl(e,t,s=Ye){W(e)&&(e=kn(e));for(const n in e){const r=e[n];let o;ce(r)?"default"in r?o=Dt(r.from||n,r.default,!0):o=Dt(r.from||n):o=Dt(r),we(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[n]=o}}function Sr(e,t,s){Qe(W(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function zo(e,t,s,n){let r=n.includes(".")?oi(s,n):()=>s[n];if(he(e)){const o=t[e];G(o)&&Ss(r,o)}else if(G(e))Ss(r,e.bind(s));else if(ce(e))if(W(e))e.forEach(o=>zo(o,t,s,n));else{const o=G(e.handler)?e.handler.bind(s):t[e.handler];G(o)&&Ss(r,o,e)}}function Wo(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!s&&!n?a=t:(a={},r.length&&r.forEach(u=>Ns(a,u,i,!0)),Ns(a,t,i)),ce(t)&&o.set(t,a),a}function Ns(e,t,s,n=!1){const{mixins:r,extends:o}=t;o&&Ns(e,o,s,!0),r&&r.forEach(i=>Ns(e,i,s,!0));for(const i in t)if(!(n&&i==="expose")){const l=Yl[i]||s&&s[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Yl={data:Ar,props:Rr,emits:Rr,methods:ts,computed:ts,beforeCreate:Se,created:Se,beforeMount:Se,mounted:Se,beforeUpdate:Se,updated:Se,beforeDestroy:Se,beforeUnmount:Se,destroyed:Se,unmounted:Se,activated:Se,deactivated:Se,errorCaptured:Se,serverPrefetch:Se,components:ts,directives:ts,watch:ea,provide:Ar,inject:Ql};function Ar(e,t){return t?e?function(){return Te(G(e)?e.call(this,this):e,G(t)?t.call(this,this):t)}:t:e}function Ql(e,t){return ts(kn(e),kn(t))}function kn(e){if(W(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Se(e,t){return e?[...new Set([].concat(e,t))]:t}function ts(e,t){return e?Te(Object.create(null),e,t):t}function Rr(e,t){return e?W(e)&&W(t)?[...new Set([...e,...t])]:Te(Object.create(null),Cr(e),Cr(t??{})):t}function ea(e,t){if(!e)return t;if(!t)return e;const s=Te(Object.create(null),e);for(const n in t)s[n]=Se(e[n],t[n]);return s}function Ko(){return{app:null,config:{isNativeTag:ji,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ta=0;function sa(e,t){return function(n,r=null){G(n)||(n=Te({},n)),r!=null&&!ce(r)&&(r=null);const o=Ko(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:ta++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:Na,get config(){return o.config},set config(c){},use(c,...d){return i.has(c)||(c&&G(c.install)?(i.add(c),c.install(u,...d)):G(c)&&(i.add(c),c(u,...d))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,d){return d?(o.components[c]=d,u):o.components[c]},directive(c,d){return d?(o.directives[c]=d,u):o.directives[c]},mount(c,d,y){if(!a){const m=u._ceVNode||pe(n,r);return m.appContext=o,y===!0?y="svg":y===!1&&(y=void 0),e(m,c,y),a=!0,u._container=c,c.__vue_app__=u,sn(m.component)}},onUnmount(c){l.push(c)},unmount(){a&&(Qe(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,d){return o.provides[c]=d,u},runWithContext(c){const d=Ut;Ut=u;try{return c()}finally{Ut=d}}};return u}}let Ut=null;function na(e,t){if(ve){let s=ve.provides;const n=ve.parent&&ve.parent.provides;n===s&&(s=ve.provides=Object.create(n)),s[e]=t}}function Dt(e,t,s=!1){const n=ve||ye;if(n||Ut){let r=Ut?Ut._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&G(t)?t.call(n&&n.proxy):t}}const Jo={},Zo=()=>Object.create(Jo),Go=e=>Object.getPrototypeOf(e)===Jo;function ra(e,t,s,n=!1){const r={},o=Zo();e.propsDefaults=Object.create(null),Xo(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);s?e.props=n?r:xl(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function oa(e,t,s,n){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ne(r),[a]=e.propsOptions;let u=!1;if((n||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let y=c[d];if(en(e.emitsOptions,y))continue;const m=t[y];if(a)if(re(o,y))m!==o[y]&&(o[y]=m,u=!0);else{const g=Ue(y);r[g]=Ln(a,l,g,m,e,!1)}else m!==o[y]&&(o[y]=m,u=!0)}}}else{Xo(e,t,r,o)&&(u=!0);let c;for(const d in l)(!t||!re(t,d)&&((c=Et(d))===d||!re(t,c)))&&(a?s&&(s[d]!==void 0||s[c]!==void 0)&&(r[d]=Ln(a,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!re(t,d))&&(delete o[d],u=!0)}u&&nt(e.attrs,"set","")}function Xo(e,t,s,n){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(ss(a))continue;const u=t[a];let c;r&&re(r,c=Ue(a))?!o||!o.includes(c)?s[c]=u:(l||(l={}))[c]=u:en(e.emitsOptions,a)||(!(a in n)||u!==n[a])&&(n[a]=u,i=!0)}if(o){const a=ne(s),u=l||ie;for(let c=0;c<o.length;c++){const d=o[c];s[d]=Ln(r,a,d,u[d],e,!re(u,d))}}return i}function Ln(e,t,s,n,r,o){const i=e[s];if(i!=null){const l=re(i,"default");if(l&&n===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&G(a)){const{propsDefaults:u}=r;if(s in u)n=u[s];else{const c=ms(r);n=u[s]=a.call(null,t),c()}}else n=a;r.ce&&r.ce._setProp(s,n)}i[0]&&(o&&!l?n=!1:i[1]&&(n===""||n===Et(s))&&(n=!0))}return n}const ia=new WeakMap;function Yo(e,t,s=!1){const n=s?ia:t.propsCache,r=n.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!G(e)){const c=d=>{a=!0;const[y,m]=Yo(d,t,!0);Te(i,y),m&&l.push(...m)};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return ce(e)&&n.set(e,kt),kt;if(W(o))for(let c=0;c<o.length;c++){const d=Ue(o[c]);Tr(d)&&(i[d]=ie)}else if(o)for(const c in o){const d=Ue(c);if(Tr(d)){const y=o[c],m=i[d]=W(y)||G(y)?{type:y}:Te({},y),g=m.type;let C=!1,w=!0;if(W(g))for(let k=0;k<g.length;++k){const D=g[k],K=G(D)&&D.name;if(K==="Boolean"){C=!0;break}else K==="String"&&(w=!1)}else C=G(g)&&g.name==="Boolean";m[0]=C,m[1]=w,(C||re(m,"default"))&&l.push(d)}}const u=[i,l];return ce(e)&&n.set(e,u),u}function Tr(e){return e[0]!=="$"&&!ss(e)}const rr=e=>e[0]==="_"||e==="$stable",or=e=>W(e)?e.map(Xe):[Xe(e)],la=(e,t,s)=>{if(t._n)return t;const n=Uo((...r)=>or(t(...r)),s);return n._c=!1,n},Qo=(e,t,s)=>{const n=e._ctx;for(const r in e){if(rr(r))continue;const o=e[r];if(G(o))t[r]=la(r,o,n);else if(o!=null){const i=or(o);t[r]=()=>i}}},ei=(e,t)=>{const s=or(t);e.slots.default=()=>s},ti=(e,t,s)=>{for(const n in t)(s||!rr(n))&&(e[n]=t[n])},aa=(e,t,s)=>{const n=e.slots=Zo();if(e.vnode.shapeFlag&32){const r=t._;r?(ti(n,t,s),s&&ho(n,"_",r,!0)):Qo(t,n)}else t&&ei(e,t)},ca=(e,t,s)=>{const{vnode:n,slots:r}=e;let o=!0,i=ie;if(n.shapeFlag&32){const l=t._;l?s&&l===1?o=!1:ti(r,t,s):(o=!t.$stable,Qo(t,r)),i=t}else t&&(ei(e,t),i={default:1});if(o)for(const l in r)!rr(l)&&i[l]==null&&delete r[l]},Le=Ca;function ua(e){return fa(e)}function fa(e,t){const s=Ks();s.__VUE__=!0;const{insert:n,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:d,nextSibling:y,setScopeId:m=Ye,insertStaticContent:g}=e,C=(f,h,x,R=null,S=null,A=null,$=void 0,P=null,F=!!h.dynamicChildren)=>{if(f===h)return;f&&!Yt(f,h)&&(R=vs(f),qe(f,S,A,!0),f=null),h.patchFlag===-2&&(F=!1,h.dynamicChildren=null);const{type:T,ref:V,shapeFlag:M}=h;switch(T){case tn:w(f,h,x,R);break;case lt:k(f,h,x,R);break;case As:f==null&&D(h,x,R,$);break;case de:E(f,h,x,R,S,A,$,P,F);break;default:M&1?X(f,h,x,R,S,A,$,P,F):M&6?L(f,h,x,R,S,A,$,P,F):(M&64||M&128)&&T.process(f,h,x,R,S,A,$,P,F,Zt)}V!=null&&S&&Is(V,f&&f.ref,A,h||f,!h)},w=(f,h,x,R)=>{if(f==null)n(h.el=l(h.children),x,R);else{const S=h.el=f.el;h.children!==f.children&&u(S,h.children)}},k=(f,h,x,R)=>{f==null?n(h.el=a(h.children||""),x,R):h.el=f.el},D=(f,h,x,R)=>{[f.el,f.anchor]=g(f.children,h,x,R,f.el,f.anchor)},K=({el:f,anchor:h},x,R)=>{let S;for(;f&&f!==h;)S=y(f),n(f,x,R),f=S;n(h,x,R)},I=({el:f,anchor:h})=>{let x;for(;f&&f!==h;)x=y(f),r(f),f=x;r(h)},X=(f,h,x,R,S,A,$,P,F)=>{h.type==="svg"?$="svg":h.type==="math"&&($="mathml"),f==null?fe(h,x,R,S,A,$,P,F):v(f,h,S,A,$,P,F)},fe=(f,h,x,R,S,A,$,P)=>{let F,T;const{props:V,shapeFlag:M,transition:j,dirs:J}=f;if(F=f.el=i(f.type,A,V&&V.is,V),M&8?c(F,f.children):M&16&&U(f.children,F,null,R,S,xn(f,A),$,P),J&&bt(f,null,R,"created"),ue(F,f,f.scopeId,$,R),V){for(const le in V)le!=="value"&&!ss(le)&&o(F,le,null,V[le],A,R);"value"in V&&o(F,"value",null,V.value,A),(T=V.onVnodeBeforeMount)&&Je(T,R,f)}J&&bt(f,null,R,"beforeMount");const te=da(S,j);te&&j.beforeEnter(F),n(F,h,x),((T=V&&V.onVnodeMounted)||te||J)&&Le(()=>{T&&Je(T,R,f),te&&j.enter(F),J&&bt(f,null,R,"mounted")},S)},ue=(f,h,x,R,S)=>{if(x&&m(f,x),R)for(let A=0;A<R.length;A++)m(f,R[A]);if(S){let A=S.subTree;if(h===A||li(A.type)&&(A.ssContent===h||A.ssFallback===h)){const $=S.vnode;ue(f,$,$.scopeId,$.slotScopeIds,S.parent)}}},U=(f,h,x,R,S,A,$,P,F=0)=>{for(let T=F;T<f.length;T++){const V=f[T]=P?dt(f[T]):Xe(f[T]);C(null,V,h,x,R,S,A,$,P)}},v=(f,h,x,R,S,A,$)=>{const P=h.el=f.el;let{patchFlag:F,dynamicChildren:T,dirs:V}=h;F|=f.patchFlag&16;const M=f.props||ie,j=h.props||ie;let J;if(x&&vt(x,!1),(J=j.onVnodeBeforeUpdate)&&Je(J,x,h,f),V&&bt(h,f,x,"beforeUpdate"),x&&vt(x,!0),(M.innerHTML&&j.innerHTML==null||M.textContent&&j.textContent==null)&&c(P,""),T?_(f.dynamicChildren,T,P,x,R,xn(h,S),A):$||ee(f,h,P,null,x,R,xn(h,S),A,!1),F>0){if(F&16)O(P,M,j,x,S);else if(F&2&&M.class!==j.class&&o(P,"class",null,j.class,S),F&4&&o(P,"style",M.style,j.style,S),F&8){const te=h.dynamicProps;for(let le=0;le<te.length;le++){const oe=te[le],Me=M[oe],Ee=j[oe];(Ee!==Me||oe==="value")&&o(P,oe,Me,Ee,S,x)}}F&1&&f.children!==h.children&&c(P,h.children)}else!$&&T==null&&O(P,M,j,x,S);((J=j.onVnodeUpdated)||V)&&Le(()=>{J&&Je(J,x,h,f),V&&bt(h,f,x,"updated")},R)},_=(f,h,x,R,S,A,$)=>{for(let P=0;P<h.length;P++){const F=f[P],T=h[P],V=F.el&&(F.type===de||!Yt(F,T)||F.shapeFlag&198)?d(F.el):x;C(F,T,V,null,R,S,A,$,!0)}},O=(f,h,x,R,S)=>{if(h!==x){if(h!==ie)for(const A in h)!ss(A)&&!(A in x)&&o(f,A,h[A],null,S,R);for(const A in x){if(ss(A))continue;const $=x[A],P=h[A];$!==P&&A!=="value"&&o(f,A,P,$,S,R)}"value"in x&&o(f,"value",h.value,x.value,S)}},E=(f,h,x,R,S,A,$,P,F)=>{const T=h.el=f?f.el:l(""),V=h.anchor=f?f.anchor:l("");let{patchFlag:M,dynamicChildren:j,slotScopeIds:J}=h;J&&(P=P?P.concat(J):J),f==null?(n(T,x,R),n(V,x,R),U(h.children||[],x,V,S,A,$,P,F)):M>0&&M&64&&j&&f.dynamicChildren?(_(f.dynamicChildren,j,x,S,A,$,P),(h.key!=null||S&&h===S.subTree)&&si(f,h,!0)):ee(f,h,x,V,S,A,$,P,F)},L=(f,h,x,R,S,A,$,P,F)=>{h.slotScopeIds=P,f==null?h.shapeFlag&512?S.ctx.activate(h,x,R,$,F):H(h,x,R,S,A,$,F):q(f,h,F)},H=(f,h,x,R,S,A,$)=>{const P=f.component=Oa(f,R,S);if(jo(f)&&(P.ctx.renderer=Zt),Pa(P,!1,$),P.asyncDep){if(S&&S.registerDep(P,Q,$),!f.el){const F=P.subTree=pe(lt);k(null,F,h,x)}}else Q(P,f,h,x,S,A,$)},q=(f,h,x)=>{const R=h.component=f.component;if(_a(f,h,x))if(R.asyncDep&&!R.asyncResolved){Y(R,h,x);return}else R.next=h,R.update();else h.el=f.el,R.vnode=h},Q=(f,h,x,R,S,A,$)=>{const P=()=>{if(f.isMounted){let{next:M,bu:j,u:J,parent:te,vnode:le}=f;{const We=ni(f);if(We){M&&(M.el=le.el,Y(f,M,$)),We.asyncDep.then(()=>{f.isUnmounted||P()});return}}let oe=M,Me;vt(f,!1),M?(M.el=le.el,Y(f,M,$)):M=le,j&&Cs(j),(Me=M.props&&M.props.onVnodeBeforeUpdate)&&Je(Me,te,M,le),vt(f,!0);const Ee=Fr(f),ze=f.subTree;f.subTree=Ee,C(ze,Ee,d(ze.el),vs(ze),f,S,A),M.el=Ee.el,oe===null&&wa(f,Ee.el),J&&Le(J,S),(Me=M.props&&M.props.onVnodeUpdated)&&Le(()=>Je(Me,te,M,le),S)}else{let M;const{el:j,props:J}=h,{bm:te,m:le,parent:oe,root:Me,type:Ee}=f,ze=Ht(h);vt(f,!1),te&&Cs(te),!ze&&(M=J&&J.onVnodeBeforeMount)&&Je(M,oe,h),vt(f,!0);{Me.ce&&Me.ce._injectChildStyle(Ee);const We=f.subTree=Fr(f);C(null,We,x,R,f,S,A),h.el=We.el}if(le&&Le(le,S),!ze&&(M=J&&J.onVnodeMounted)){const We=h;Le(()=>Je(M,oe,We),S)}(h.shapeFlag&256||oe&&Ht(oe.vnode)&&oe.vnode.shapeFlag&256)&&f.a&&Le(f.a,S),f.isMounted=!0,h=x=R=null}};f.scope.on();const F=f.effect=new bo(P);f.scope.off();const T=f.update=F.run.bind(F),V=f.job=F.runIfDirty.bind(F);V.i=f,V.id=f.uid,F.scheduler=()=>tr(V),vt(f,!0),T()},Y=(f,h,x)=>{h.component=f;const R=f.vnode.props;f.vnode=h,f.next=null,oa(f,h.props,R,x),ca(f,h.children,x),ot(),_r(f),it()},ee=(f,h,x,R,S,A,$,P,F=!1)=>{const T=f&&f.children,V=f?f.shapeFlag:0,M=h.children,{patchFlag:j,shapeFlag:J}=h;if(j>0){if(j&128){bs(T,M,x,R,S,A,$,P,F);return}else if(j&256){gt(T,M,x,R,S,A,$,P,F);return}}J&8?(V&16&&Jt(T,S,A),M!==T&&c(x,M)):V&16?J&16?bs(T,M,x,R,S,A,$,P,F):Jt(T,S,A,!0):(V&8&&c(x,""),J&16&&U(M,x,R,S,A,$,P,F))},gt=(f,h,x,R,S,A,$,P,F)=>{f=f||kt,h=h||kt;const T=f.length,V=h.length,M=Math.min(T,V);let j;for(j=0;j<M;j++){const J=h[j]=F?dt(h[j]):Xe(h[j]);C(f[j],J,x,null,S,A,$,P,F)}T>V?Jt(f,S,A,!0,!1,M):U(h,x,R,S,A,$,P,F,M)},bs=(f,h,x,R,S,A,$,P,F)=>{let T=0;const V=h.length;let M=f.length-1,j=V-1;for(;T<=M&&T<=j;){const J=f[T],te=h[T]=F?dt(h[T]):Xe(h[T]);if(Yt(J,te))C(J,te,x,null,S,A,$,P,F);else break;T++}for(;T<=M&&T<=j;){const J=f[M],te=h[j]=F?dt(h[j]):Xe(h[j]);if(Yt(J,te))C(J,te,x,null,S,A,$,P,F);else break;M--,j--}if(T>M){if(T<=j){const J=j+1,te=J<V?h[J].el:R;for(;T<=j;)C(null,h[T]=F?dt(h[T]):Xe(h[T]),x,te,S,A,$,P,F),T++}}else if(T>j)for(;T<=M;)qe(f[T],S,A,!0),T++;else{const J=T,te=T,le=new Map;for(T=te;T<=j;T++){const ke=h[T]=F?dt(h[T]):Xe(h[T]);ke.key!=null&&le.set(ke.key,T)}let oe,Me=0;const Ee=j-te+1;let ze=!1,We=0;const Gt=new Array(Ee);for(T=0;T<Ee;T++)Gt[T]=0;for(T=J;T<=M;T++){const ke=f[T];if(Me>=Ee){qe(ke,S,A,!0);continue}let Ke;if(ke.key!=null)Ke=le.get(ke.key);else for(oe=te;oe<=j;oe++)if(Gt[oe-te]===0&&Yt(ke,h[oe])){Ke=oe;break}Ke===void 0?qe(ke,S,A,!0):(Gt[Ke-te]=T+1,Ke>=We?We=Ke:ze=!0,C(ke,h[Ke],x,null,S,A,$,P,F),Me++)}const mr=ze?pa(Gt):kt;for(oe=mr.length-1,T=Ee-1;T>=0;T--){const ke=te+T,Ke=h[ke],gr=ke+1<V?h[ke+1].el:R;Gt[T]===0?C(null,Ke,x,gr,S,A,$,P,F):ze&&(oe<0||T!==mr[oe]?yt(Ke,x,gr,2):oe--)}}},yt=(f,h,x,R,S=null)=>{const{el:A,type:$,transition:P,children:F,shapeFlag:T}=f;if(T&6){yt(f.component.subTree,h,x,R);return}if(T&128){f.suspense.move(h,x,R);return}if(T&64){$.move(f,h,x,Zt);return}if($===de){n(A,h,x);for(let M=0;M<F.length;M++)yt(F[M],h,x,R);n(f.anchor,h,x);return}if($===As){K(f,h,x);return}if(R!==2&&T&1&&P)if(R===0)P.beforeEnter(A),n(A,h,x),Le(()=>P.enter(A),S);else{const{leave:M,delayLeave:j,afterLeave:J}=P,te=()=>{f.ctx.isUnmounted?r(A):n(A,h,x)},le=()=>{M(A,()=>{te(),J&&J()})};j?j(A,te,le):le()}else n(A,h,x)},qe=(f,h,x,R=!1,S=!1)=>{const{type:A,props:$,ref:P,children:F,dynamicChildren:T,shapeFlag:V,patchFlag:M,dirs:j,cacheIndex:J}=f;if(M===-2&&(S=!1),P!=null&&(ot(),Is(P,null,x,f,!0),it()),J!=null&&(h.renderCache[J]=void 0),V&256){h.ctx.deactivate(f);return}const te=V&1&&j,le=!Ht(f);let oe;if(le&&(oe=$&&$.onVnodeBeforeUnmount)&&Je(oe,h,f),V&6)Di(f.component,x,R);else{if(V&128){f.suspense.unmount(x,R);return}te&&bt(f,null,h,"beforeUnmount"),V&64?f.type.remove(f,h,x,Zt,R):T&&!T.hasOnce&&(A!==de||M>0&&M&64)?Jt(T,h,x,!1,!0):(A===de&&M&384||!S&&V&16)&&Jt(F,h,x),R&&pr(f)}(le&&(oe=$&&$.onVnodeUnmounted)||te)&&Le(()=>{oe&&Je(oe,h,f),te&&bt(f,null,h,"unmounted")},x)},pr=f=>{const{type:h,el:x,anchor:R,transition:S}=f;if(h===de){Ui(x,R);return}if(h===As){I(f);return}const A=()=>{r(x),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(f.shapeFlag&1&&S&&!S.persisted){const{leave:$,delayLeave:P}=S,F=()=>$(x,A);P?P(f.el,A,F):F()}else A()},Ui=(f,h)=>{let x;for(;f!==h;)x=y(f),r(f),f=x;r(h)},Di=(f,h,x)=>{const{bum:R,scope:S,job:A,subTree:$,um:P,m:F,a:T,parent:V,slots:{__:M}}=f;Er(F),Er(T),R&&Cs(R),V&&W(M)&&M.forEach(j=>{V.renderCache[j]=void 0}),S.stop(),A&&(A.flags|=8,qe($,f,h,x)),P&&Le(P,h),Le(()=>{f.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Jt=(f,h,x,R=!1,S=!1,A=0)=>{for(let $=A;$<f.length;$++)qe(f[$],h,x,R,S)},vs=f=>{if(f.shapeFlag&6)return vs(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const h=y(f.anchor||f.el),x=h&&h[$l];return x?y(x):h};let pn=!1;const hr=(f,h,x)=>{f==null?h._vnode&&qe(h._vnode,null,null,!0):C(h._vnode||null,f,h,null,null,null,x),h._vnode=f,pn||(pn=!0,_r(),Io(),pn=!1)},Zt={p:C,um:qe,m:yt,r:pr,mt:H,mc:U,pc:ee,pbc:_,n:vs,o:e};return{render:hr,hydrate:void 0,createApp:sa(hr)}}function xn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function vt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function da(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function si(e,t,s=!1){const n=e.children,r=t.children;if(W(n)&&W(r))for(let o=0;o<n.length;o++){const i=n[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=dt(r[o]),l.el=i.el),!s&&l.patchFlag!==-2&&si(i,l)),l.type===tn&&(l.el=i.el),l.type===lt&&!l.el&&(l.el=i.el)}}function pa(e){const t=e.slice(),s=[0];let n,r,o,i,l;const a=e.length;for(n=0;n<a;n++){const u=e[n];if(u!==0){if(r=s[s.length-1],e[r]<u){t[n]=r,s.push(n);continue}for(o=0,i=s.length-1;o<i;)l=o+i>>1,e[s[l]]<u?o=l+1:i=l;u<e[s[o]]&&(o>0&&(t[n]=s[o-1]),s[o]=n)}}for(o=s.length,i=s[o-1];o-- >0;)s[o]=i,i=t[i];return s}function ni(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ni(t)}function Er(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ha=Symbol.for("v-scx"),ma=()=>Dt(ha);function Ss(e,t,s){return ri(e,t,s)}function ri(e,t,s=ie){const{immediate:n,deep:r,flush:o,once:i}=s,l=Te({},s),a=t&&n||!t&&o!=="post";let u;if(fs){if(o==="sync"){const m=ma();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=Ye,m.resume=Ye,m.pause=Ye,m}}const c=ve;l.call=(m,g,C)=>Qe(m,c,g,C);let d=!1;o==="post"?l.scheduler=m=>{Le(m,c&&c.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(m,g)=>{g?m():tr(m)}),l.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const y=El(e,t,l);return fs&&(u?u.push(y):a&&y()),y}function ga(e,t,s){const n=this.proxy,r=he(e)?e.includes(".")?oi(n,e):()=>n[e]:e.bind(n,n);let o;G(t)?o=t:(o=t.handler,s=t);const i=ms(this),l=ri(r,o.bind(n),s);return i(),l}function oi(e,t){const s=t.split(".");return()=>{let n=e;for(let r=0;r<s.length&&n;r++)n=n[s[r]];return n}}const ya=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ue(t)}Modifiers`]||e[`${Et(t)}Modifiers`];function ba(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||ie;let r=s;const o=t.startsWith("update:"),i=o&&ya(n,t.slice(7));i&&(i.trim&&(r=s.map(c=>he(c)?c.trim():c)),i.number&&(r=s.map(Ps)));let l,a=n[l=hn(t)]||n[l=hn(Ue(t))];!a&&o&&(a=n[l=hn(Et(t))]),a&&Qe(a,e,6,r);const u=n[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Qe(u,e,6,r)}}function ii(e,t,s=!1){const n=t.emitsCache,r=n.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!G(e)){const a=u=>{const c=ii(u,t,!0);c&&(l=!0,Te(i,c))};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ce(e)&&n.set(e,null),null):(W(o)?o.forEach(a=>i[a]=null):Te(i,o),ce(e)&&n.set(e,i),i)}function en(e,t){return!e||!Vs(t)?!1:(t=t.slice(2).replace(/Once$/,""),re(e,t[0].toLowerCase()+t.slice(1))||re(e,Et(t))||re(e,t))}function Fr(e){const{type:t,vnode:s,proxy:n,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:d,data:y,setupState:m,ctx:g,inheritAttrs:C}=e,w=Ls(e);let k,D;try{if(s.shapeFlag&4){const I=r||n,X=I;k=Xe(u.call(X,I,c,d,m,y,g)),D=l}else{const I=t;k=Xe(I.length>1?I(d,{attrs:l,slots:i,emit:a}):I(d,null)),D=t.props?l:va(l)}}catch(I){is.length=0,Ys(I,e,1),k=pe(lt)}let K=k;if(D&&C!==!1){const I=Object.keys(D),{shapeFlag:X}=K;I.length&&X&7&&(o&&I.some(zn)&&(D=xa(D,o)),K=Vt(K,D,!1,!0))}return s.dirs&&(K=Vt(K,null,!1,!0),K.dirs=K.dirs?K.dirs.concat(s.dirs):s.dirs),s.transition&&sr(K,s.transition),k=K,Ls(w),k}const va=e=>{let t;for(const s in e)(s==="class"||s==="style"||Vs(s))&&((t||(t={}))[s]=e[s]);return t},xa=(e,t)=>{const s={};for(const n in e)(!zn(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function _a(e,t,s){const{props:n,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&a>=0){if(a&1024)return!0;if(a&16)return n?Or(n,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const y=c[d];if(i[y]!==n[y]&&!en(u,y))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:n===i?!1:n?i?Or(n,i,u):!0:!!i;return!1}function Or(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let r=0;r<n.length;r++){const o=n[r];if(t[o]!==e[o]&&!en(s,o))return!0}return!1}function wa({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const li=e=>e.__isSuspense;function Ca(e,t){t&&t.pendingBranch?W(e)?t.effects.push(...e):t.effects.push(e):Pl(e)}const de=Symbol.for("v-fgt"),tn=Symbol.for("v-txt"),lt=Symbol.for("v-cmt"),As=Symbol.for("v-stc"),is=[];let Ie=null;function N(e=!1){is.push(Ie=e?null:[])}function Sa(){is.pop(),Ie=is[is.length-1]||null}let us=1;function Pr(e,t=!1){us+=e,e<0&&Ie&&t&&(Ie.hasOnce=!0)}function ai(e){return e.dynamicChildren=us>0?Ie||kt:null,Sa(),us>0&&Ie&&Ie.push(e),e}function B(e,t,s,n,r,o){return ai(p(e,t,s,n,r,o,!0))}function Bt(e,t,s,n,r){return ai(pe(e,t,s,n,r,!0))}function ir(e){return e?e.__v_isVNode===!0:!1}function Yt(e,t){return e.type===t.type&&e.key===t.key}const ci=({key:e})=>e??null,Rs=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||we(e)||G(e)?{i:ye,r:e,k:t,f:!!s}:e:null);function p(e,t=null,s=null,n=0,r=null,o=e===de?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ci(t),ref:t&&Rs(t),scopeId:Ho,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:n,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ye};return l?(lr(a,s),o&128&&e.normalize(a)):s&&(a.shapeFlag|=he(s)?8:16),us>0&&!i&&Ie&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Ie.push(a),a}const pe=Aa;function Aa(e,t=null,s=null,n=0,r=null,o=!1){if((!e||e===Vo)&&(e=lt),ir(e)){const l=Vt(e,t,!0);return s&&lr(l,s),us>0&&!o&&Ie&&(l.shapeFlag&6?Ie[Ie.indexOf(e)]=l:Ie.push(l)),l.patchFlag=-2,l}if(Ia(e)&&(e=e.__vccOpts),t){t=Ra(t);let{class:l,style:a}=t;l&&!he(l)&&(t.class=_e(l)),ce(a)&&(er(a)&&!W(a)&&(a=Te({},a)),t.style=Js(a))}const i=he(e)?1:li(e)?128:Ml(e)?64:ce(e)?4:G(e)?2:0;return p(e,t,s,n,r,i,o,!0)}function Ra(e){return e?er(e)||Go(e)?Te({},e):e:null}function Vt(e,t,s=!1,n=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?Ta(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&ci(u),ref:t&&t.ref?s&&o?W(o)?o.concat(Rs(t)):[o,Rs(t)]:Rs(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==de?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Vt(e.ssContent),ssFallback:e.ssFallback&&Vt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&n&&sr(c,a.clone(c)),c}function Re(e=" ",t=0){return pe(tn,null,e,t)}function He(e,t){const s=pe(As,null,e);return s.staticCount=t,s}function Ce(e="",t=!1){return t?(N(),Bt(lt,null,e)):pe(lt,null,e)}function Xe(e){return e==null||typeof e=="boolean"?pe(lt):W(e)?pe(de,null,e.slice()):ir(e)?dt(e):pe(tn,null,String(e))}function dt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Vt(e)}function lr(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(W(t))s=16;else if(typeof t=="object")if(n&65){const r=t.default;r&&(r._c&&(r._d=!1),lr(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!Go(t)?t._ctx=ye:r===3&&ye&&(ye.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else G(t)?(t={default:t,_ctx:ye},s=32):(t=String(t),n&64?(s=16,t=[Re(t)]):s=8);e.children=t,e.shapeFlag|=s}function Ta(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const r in n)if(r==="class")t.class!==n.class&&(t.class=_e([t.class,n.class]));else if(r==="style")t.style=Js([t.style,n.style]);else if(Vs(r)){const o=t[r],i=n[r];i&&o!==i&&!(W(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=n[r])}return t}function Je(e,t,s,n=null){Qe(e,t,7,[s,n])}const Ea=Ko();let Fa=0;function Oa(e,t,s){const n=e.type,r=(t?t.appContext:e.appContext)||Ea,o={uid:Fa++,vnode:e,type:n,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new el(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Yo(n,r),emitsOptions:ii(n,r),emit:null,emitted:null,propsDefaults:ie,inheritAttrs:n.inheritAttrs,ctx:ie,data:ie,props:ie,attrs:ie,slots:ie,refs:ie,setupState:ie,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ba.bind(null,o),e.ce&&e.ce(o),o}let ve=null,Hs,In;{const e=Ks(),t=(s,n)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(n),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Hs=t("__VUE_INSTANCE_SETTERS__",s=>ve=s),In=t("__VUE_SSR_SETTERS__",s=>fs=s)}const ms=e=>{const t=ve;return Hs(e),e.scope.on(),()=>{e.scope.off(),Hs(t)}},$r=()=>{ve&&ve.scope.off(),Hs(null)};function ui(e){return e.vnode.shapeFlag&4}let fs=!1;function Pa(e,t=!1,s=!1){t&&In(t);const{props:n,children:r}=e.vnode,o=ui(e);ra(e,n,o,t),aa(e,r,s||t);const i=o?$a(e,t):void 0;return t&&In(!1),i}function $a(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Zl);const{setup:n}=s;if(n){ot();const r=e.setupContext=n.length>1?ka(e):null,o=ms(e),i=hs(n,e,0,[e.props,r]),l=uo(i);if(it(),o(),(l||e.sp)&&!Ht(e)&&Do(e),l){if(i.then($r,$r),t)return i.then(a=>{Mr(e,a)}).catch(a=>{Ys(a,e,0)});e.asyncDep=i}else Mr(e,i)}else fi(e)}function Mr(e,t,s){G(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ce(t)&&(e.setupState=Mo(t)),fi(e)}function fi(e,t,s){const n=e.type;e.render||(e.render=n.render||Ye);{const r=ms(e);ot();try{Gl(e)}finally{it(),r()}}}const Ma={get(e,t){return be(e,"get",""),e[t]}};function ka(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Ma),slots:e.slots,emit:e.emit,expose:t}}function sn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Mo(_l(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in os)return os[s](e)},has(t,s){return s in t||s in os}})):e.proxy}function La(e,t=!0){return G(e)?e.displayName||e.name:e.name||t&&e.__name}function Ia(e){return G(e)&&"__vccOpts"in e}const $e=(e,t)=>Rl(e,t,fs),Na="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Nn;const kr=typeof window<"u"&&window.trustedTypes;if(kr)try{Nn=kr.createPolicy("vue",{createHTML:e=>e})}catch{}const di=Nn?e=>Nn.createHTML(e):e=>e,Ha="http://www.w3.org/2000/svg",Ua="http://www.w3.org/1998/Math/MathML",st=typeof document<"u"?document:null,Lr=st&&st.createElement("template"),Da={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const r=t==="svg"?st.createElementNS(Ha,e):t==="mathml"?st.createElementNS(Ua,e):s?st.createElement(e,{is:s}):st.createElement(e);return e==="select"&&n&&n.multiple!=null&&r.setAttribute("multiple",n.multiple),r},createText:e=>st.createTextNode(e),createComment:e=>st.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>st.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,r,o){const i=s?s.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===o||!(r=r.nextSibling)););else{Lr.innerHTML=di(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=Lr.content;if(n==="svg"||n==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,s)}return[i?i.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},ja=Symbol("_vtc");function Ba(e,t,s){const n=e[ja];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Ir=Symbol("_vod"),Va=Symbol("_vsh"),qa=Symbol(""),za=/(^|;)\s*display\s*:/;function Wa(e,t,s){const n=e.style,r=he(s);let o=!1;if(s&&!r){if(t)if(he(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();s[l]==null&&Ts(n,l,"")}else for(const i in t)s[i]==null&&Ts(n,i,"");for(const i in s)i==="display"&&(o=!0),Ts(n,i,s[i])}else if(r){if(t!==s){const i=n[qa];i&&(s+=";"+i),n.cssText=s,o=za.test(s)}}else t&&e.removeAttribute("style");Ir in e&&(e[Ir]=o?n.display:"",e[Va]&&(n.display="none"))}const Nr=/\s*!important$/;function Ts(e,t,s){if(W(s))s.forEach(n=>Ts(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=Ka(e,t);Nr.test(s)?e.setProperty(Et(n),s.replace(Nr,""),"important"):e[n]=s}}const Hr=["Webkit","Moz","ms"],_n={};function Ka(e,t){const s=_n[t];if(s)return s;let n=Ue(t);if(n!=="filter"&&n in e)return _n[t]=n;n=Ws(n);for(let r=0;r<Hr.length;r++){const o=Hr[r]+n;if(o in e)return _n[t]=o}return t}const Ur="http://www.w3.org/1999/xlink";function Dr(e,t,s,n,r,o=Xi(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Ur,t.slice(6,t.length)):e.setAttributeNS(Ur,t,s):s==null||o&&!mo(s)?e.removeAttribute(t):e.setAttribute(t,o?"":je(s)?String(s):s)}function jr(e,t,s,n,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?di(s):s);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=s==null?e.type==="checkbox"?"on":"":String(s);(l!==a||!("_value"in e))&&(e.value=a),s==null&&e.removeAttribute(t),e._value=s;return}let i=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=mo(s):s==null&&l==="string"?(s="",i=!0):l==="number"&&(s=0,i=!0)}try{e[t]=s}catch{}i&&e.removeAttribute(r||t)}function _t(e,t,s,n){e.addEventListener(t,s,n)}function Ja(e,t,s,n){e.removeEventListener(t,s,n)}const Br=Symbol("_vei");function Za(e,t,s,n,r=null){const o=e[Br]||(e[Br]={}),i=o[t];if(n&&i)i.value=n;else{const[l,a]=Ga(t);if(n){const u=o[t]=Qa(n,r);_t(e,l,u,a)}else i&&(Ja(e,l,i,a),o[t]=void 0)}}const Vr=/(?:Once|Passive|Capture)$/;function Ga(e){let t;if(Vr.test(e)){t={};let n;for(;n=e.match(Vr);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Et(e.slice(2)),t]}let wn=0;const Xa=Promise.resolve(),Ya=()=>wn||(Xa.then(()=>wn=0),wn=Date.now());function Qa(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Qe(ec(n,s.value),t,5,[n])};return s.value=e,s.attached=Ya(),s}function ec(e,t){if(W(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>r=>!r._stopped&&n&&n(r))}else return t}const qr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,tc=(e,t,s,n,r,o)=>{const i=r==="svg";t==="class"?Ba(e,n,i):t==="style"?Wa(e,s,n):Vs(t)?zn(t)||Za(e,t,s,n,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):sc(e,t,n,i))?(jr(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Dr(e,t,n,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!he(n))?jr(e,Ue(t),n,o,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Dr(e,t,n,i))};function sc(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&qr(t)&&G(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return qr(t)&&he(s)?!1:t in e}const Us=e=>{const t=e.props["onUpdate:modelValue"]||!1;return W(t)?s=>Cs(t,s):t};function nc(e){e.target.composing=!0}function zr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const jt=Symbol("_assign"),rc={created(e,{modifiers:{lazy:t,trim:s,number:n}},r){e[jt]=Us(r);const o=n||r.props&&r.props.type==="number";_t(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;s&&(l=l.trim()),o&&(l=Ps(l)),e[jt](l)}),s&&_t(e,"change",()=>{e.value=e.value.trim()}),t||(_t(e,"compositionstart",nc),_t(e,"compositionend",zr),_t(e,"change",zr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:r,number:o}},i){if(e[jt]=Us(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Ps(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||r&&e.value.trim()===a)||(e.value=a))}},qt={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const r=qs(t);_t(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>s?Ps(Ds(i)):Ds(i));e[jt](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,zt(()=>{e._assigning=!1})}),e[jt]=Us(n)},mounted(e,{value:t}){Wr(e,t)},beforeUpdate(e,t,s){e[jt]=Us(s)},updated(e,{value:t}){e._assigning||Wr(e,t)}};function Wr(e,t){const s=e.multiple,n=W(t);if(!(s&&!n&&!qs(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=Ds(i);if(s)if(n){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(u=>String(u)===String(l)):i.selected=Qi(t,l)>-1}else i.selected=t.has(l);else if(Zs(Ds(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Ds(e){return"_value"in e?e._value:e.value}const oc=Te({patchProp:tc},Da);let Kr;function ic(){return Kr||(Kr=ua(oc))}const lc=(...e)=>{const t=ic().createApp(...e),{mount:s}=t;return t.mount=n=>{const r=cc(n);if(!r)return;const o=t._component;!G(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=s(r,!1,ac(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function ac(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function cc(e){return he(e)?document.querySelector(e):e}const uc="/rmscrmseries/public/crmbuild/images/niswey-hubspot.png",fc="/rmscrmseries/public/crmbuild/images/HubSpot.png";/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */var Jr;(function(e){e.pop="pop",e.push="push"})(Jr||(Jr={}));var Zr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Zr||(Zr={}));var Gr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Gr||(Gr={}));const dc=Symbol(""),pc=Symbol("");function hc(){return Dt(dc)}function mc(e){return Dt(pc)}const gc={class:"flex justify-between items-center bg-gray-800 text-white h-8 px-4"},yc={class:"flex items-center space-x-2"},bc={class:"text-sm text-white"},vc={__name:"LoginHeader",setup(e){const t=mc(),s=$e(()=>t.query.email||"");return(n,r)=>(N(),B("div",null,[r[3]||(r[3]=p("div",{class:"h-10 bg-gradient-to-r from-cyan-500 to-green-400"},null,-1)),p("div",gc,[r[2]||(r[2]=p("div",{class:"flex items-center"},[p("img",{src:fc,alt:"HubSpot Logo",style:{height:"30px"}})],-1)),p("div",yc,[r[0]||(r[0]=p("div",{class:"bg-gray-700 p-1 rounded-full"},[p("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[p("path",{"fill-rule":"evenodd",d:"M10 3a5 5 0 100 10 5 5 0 000-10zM2 17a8 8 0 0116 0H2z","clip-rule":"evenodd"})])],-1)),p("div",bc,se(s.value),1),r[1]||(r[1]=p("button",{class:"focus:outline-none"},[p("svg",{class:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20"},[p("path",{d:"M5.5 7l4.5 4.5L14.5 7h-9z"})])],-1))])])]))}},xc=(e,t)=>{const s=e.__vccOpts||e;for(const[n,r]of t)s[n]=r;return s},_c={},wc={class:"bg-gray-800 text-white text-center p-4"};function Cc(e,t){return N(),B("footer",wc,t[0]||(t[0]=[Re(" © Powered by "),p("a",{href:"https://niswey.com"},"Niswey",-1)]))}const Sc=xc(_c,[["render",Cc]]),Ac={class:"flex flex-col min-h-screen"},Rc={class:"min-h-screen flex items-center justify-center"},Tc={__name:"DefaultLayout",setup(e){return(t,s)=>(N(),B("div",Ac,[pe(vc),p("main",Rc,[Jl(t.$slots,"default")]),pe(Sc)]))}};function pi(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ec}=Object.prototype,{getPrototypeOf:ar}=Object,{iterator:nn,toStringTag:hi}=Symbol,rn=(e=>t=>{const s=Ec.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Ve=e=>(e=e.toLowerCase(),t=>rn(t)===e),on=e=>t=>typeof t===e,{isArray:Wt}=Array,ds=on("undefined");function Fc(e){return e!==null&&!ds(e)&&e.constructor!==null&&!ds(e.constructor)&&Oe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const mi=Ve("ArrayBuffer");function Oc(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&mi(e.buffer),t}const Pc=on("string"),Oe=on("function"),gi=on("number"),ln=e=>e!==null&&typeof e=="object",$c=e=>e===!0||e===!1,Es=e=>{if(rn(e)!=="object")return!1;const t=ar(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(hi in e)&&!(nn in e)},Mc=Ve("Date"),kc=Ve("File"),Lc=Ve("Blob"),Ic=Ve("FileList"),Nc=e=>ln(e)&&Oe(e.pipe),Hc=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Oe(e.append)&&((t=rn(e))==="formdata"||t==="object"&&Oe(e.toString)&&e.toString()==="[object FormData]"))},Uc=Ve("URLSearchParams"),[Dc,jc,Bc,Vc]=["ReadableStream","Request","Response","Headers"].map(Ve),qc=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function gs(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let n,r;if(typeof e!="object"&&(e=[e]),Wt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const o=s?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(n=0;n<i;n++)l=o[n],t.call(null,e[l],l,e)}}function yi(e,t){t=t.toLowerCase();const s=Object.keys(e);let n=s.length,r;for(;n-- >0;)if(r=s[n],t===r.toLowerCase())return r;return null}const wt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,bi=e=>!ds(e)&&e!==wt;function Hn(){const{caseless:e}=bi(this)&&this||{},t={},s=(n,r)=>{const o=e&&yi(t,r)||r;Es(t[o])&&Es(n)?t[o]=Hn(t[o],n):Es(n)?t[o]=Hn({},n):Wt(n)?t[o]=n.slice():t[o]=n};for(let n=0,r=arguments.length;n<r;n++)arguments[n]&&gs(arguments[n],s);return t}const zc=(e,t,s,{allOwnKeys:n}={})=>(gs(t,(r,o)=>{s&&Oe(r)?e[o]=pi(r,s):e[o]=r},{allOwnKeys:n}),e),Wc=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Kc=(e,t,s,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},Jc=(e,t,s,n)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!n||n(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=s!==!1&&ar(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},Zc=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const n=e.indexOf(t,s);return n!==-1&&n===s},Gc=e=>{if(!e)return null;if(Wt(e))return e;let t=e.length;if(!gi(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},Xc=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ar(Uint8Array)),Yc=(e,t)=>{const n=(e&&e[nn]).call(e);let r;for(;(r=n.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},Qc=(e,t)=>{let s;const n=[];for(;(s=e.exec(t))!==null;)n.push(s);return n},eu=Ve("HTMLFormElement"),tu=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,r){return n.toUpperCase()+r}),Xr=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),su=Ve("RegExp"),vi=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),n={};gs(s,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(n[o]=i||r)}),Object.defineProperties(e,n)},nu=e=>{vi(e,(t,s)=>{if(Oe(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=e[s];if(Oe(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},ru=(e,t)=>{const s={},n=r=>{r.forEach(o=>{s[o]=!0})};return Wt(e)?n(e):n(String(e).split(t)),s},ou=()=>{},iu=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function lu(e){return!!(e&&Oe(e.append)&&e[hi]==="FormData"&&e[nn])}const au=e=>{const t=new Array(10),s=(n,r)=>{if(ln(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[r]=n;const o=Wt(n)?[]:{};return gs(n,(i,l)=>{const a=s(i,r+1);!ds(a)&&(o[l]=a)}),t[r]=void 0,o}}return n};return s(e,0)},cu=Ve("AsyncFunction"),uu=e=>e&&(ln(e)||Oe(e))&&Oe(e.then)&&Oe(e.catch),xi=((e,t)=>e?setImmediate:t?((s,n)=>(wt.addEventListener("message",({source:r,data:o})=>{r===wt&&o===s&&n.length&&n.shift()()},!1),r=>{n.push(r),wt.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",Oe(wt.postMessage)),fu=typeof queueMicrotask<"u"?queueMicrotask.bind(wt):typeof process<"u"&&process.nextTick||xi,du=e=>e!=null&&Oe(e[nn]),b={isArray:Wt,isArrayBuffer:mi,isBuffer:Fc,isFormData:Hc,isArrayBufferView:Oc,isString:Pc,isNumber:gi,isBoolean:$c,isObject:ln,isPlainObject:Es,isReadableStream:Dc,isRequest:jc,isResponse:Bc,isHeaders:Vc,isUndefined:ds,isDate:Mc,isFile:kc,isBlob:Lc,isRegExp:su,isFunction:Oe,isStream:Nc,isURLSearchParams:Uc,isTypedArray:Xc,isFileList:Ic,forEach:gs,merge:Hn,extend:zc,trim:qc,stripBOM:Wc,inherits:Kc,toFlatObject:Jc,kindOf:rn,kindOfTest:Ve,endsWith:Zc,toArray:Gc,forEachEntry:Yc,matchAll:Qc,isHTMLForm:eu,hasOwnProperty:Xr,hasOwnProp:Xr,reduceDescriptors:vi,freezeMethods:nu,toObjectSet:ru,toCamelCase:tu,noop:ou,toFiniteNumber:iu,findKey:yi,global:wt,isContextDefined:bi,isSpecCompliantForm:lu,toJSONObject:au,isAsyncFn:cu,isThenable:uu,setImmediate:xi,asap:fu,isIterable:du};function Z(e,t,s,n,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),n&&(this.request=n),r&&(this.response=r,this.status=r.status?r.status:null)}b.inherits(Z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const _i=Z.prototype,wi={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{wi[e]={value:e}});Object.defineProperties(Z,wi);Object.defineProperty(_i,"isAxiosError",{value:!0});Z.from=(e,t,s,n,r,o)=>{const i=Object.create(_i);return b.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),Z.call(i,e.message,t,s,n,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const pu=null;function Un(e){return b.isPlainObject(e)||b.isArray(e)}function Ci(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function Yr(e,t,s){return e?e.concat(t).map(function(r,o){return r=Ci(r),!s&&o?"["+r+"]":r}).join(s?".":""):t}function hu(e){return b.isArray(e)&&!e.some(Un)}const mu=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function an(e,t,s){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=b.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(C,w){return!b.isUndefined(w[C])});const n=s.metaTokens,r=s.visitor||c,o=s.dots,i=s.indexes,a=(s.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(r))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(b.isDate(g))return g.toISOString();if(!a&&b.isBlob(g))throw new Z("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(g)||b.isTypedArray(g)?a&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function c(g,C,w){let k=g;if(g&&!w&&typeof g=="object"){if(b.endsWith(C,"{}"))C=n?C:C.slice(0,-2),g=JSON.stringify(g);else if(b.isArray(g)&&hu(g)||(b.isFileList(g)||b.endsWith(C,"[]"))&&(k=b.toArray(g)))return C=Ci(C),k.forEach(function(K,I){!(b.isUndefined(K)||K===null)&&t.append(i===!0?Yr([C],I,o):i===null?C:C+"[]",u(K))}),!1}return Un(g)?!0:(t.append(Yr(w,C,o),u(g)),!1)}const d=[],y=Object.assign(mu,{defaultVisitor:c,convertValue:u,isVisitable:Un});function m(g,C){if(!b.isUndefined(g)){if(d.indexOf(g)!==-1)throw Error("Circular reference detected in "+C.join("."));d.push(g),b.forEach(g,function(k,D){(!(b.isUndefined(k)||k===null)&&r.call(t,k,b.isString(D)?D.trim():D,C,y))===!0&&m(k,C?C.concat(D):[D])}),d.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return m(e),t}function Qr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function cr(e,t){this._pairs=[],e&&an(e,this,t)}const Si=cr.prototype;Si.append=function(t,s){this._pairs.push([t,s])};Si.toString=function(t){const s=t?function(n){return t.call(this,n,Qr)}:Qr;return this._pairs.map(function(r){return s(r[0])+"="+s(r[1])},"").join("&")};function gu(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ai(e,t,s){if(!t)return e;const n=s&&s.encode||gu;b.isFunction(s)&&(s={serialize:s});const r=s&&s.serialize;let o;if(r?o=r(t,s):o=b.isURLSearchParams(t)?t.toString():new cr(t,s).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class eo{constructor(){this.handlers=[]}use(t,s,n){return this.handlers.push({fulfilled:t,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Ri={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},yu=typeof URLSearchParams<"u"?URLSearchParams:cr,bu=typeof FormData<"u"?FormData:null,vu=typeof Blob<"u"?Blob:null,xu={isBrowser:!0,classes:{URLSearchParams:yu,FormData:bu,Blob:vu},protocols:["http","https","file","blob","url","data"]},ur=typeof window<"u"&&typeof document<"u",Dn=typeof navigator=="object"&&navigator||void 0,_u=ur&&(!Dn||["ReactNative","NativeScript","NS"].indexOf(Dn.product)<0),wu=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Cu=ur&&window.location.href||"http://localhost",Su=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ur,hasStandardBrowserEnv:_u,hasStandardBrowserWebWorkerEnv:wu,navigator:Dn,origin:Cu},Symbol.toStringTag,{value:"Module"})),xe={...Su,...xu};function Au(e,t){return an(e,new xe.classes.URLSearchParams,Object.assign({visitor:function(s,n,r,o){return xe.isNode&&b.isBuffer(s)?(this.append(n,s.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Ru(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tu(e){const t={},s=Object.keys(e);let n;const r=s.length;let o;for(n=0;n<r;n++)o=s[n],t[o]=e[o];return t}function Ti(e){function t(s,n,r,o){let i=s[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=s.length;return i=!i&&b.isArray(r)?r.length:i,a?(b.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!l):((!r[i]||!b.isObject(r[i]))&&(r[i]=[]),t(s,n,r[i],o)&&b.isArray(r[i])&&(r[i]=Tu(r[i])),!l)}if(b.isFormData(e)&&b.isFunction(e.entries)){const s={};return b.forEachEntry(e,(n,r)=>{t(Ru(n),r,s,0)}),s}return null}function Eu(e,t,s){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(e)}const ys={transitional:Ri,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const n=s.getContentType()||"",r=n.indexOf("application/json")>-1,o=b.isObject(t);if(o&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return r?JSON.stringify(Ti(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Au(t,this.formSerializer).toString();if((l=b.isFileList(t))||n.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return an(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(s.setContentType("application/json",!1),Eu(t)):t}],transformResponse:[function(t){const s=this.transitional||ys.transitional,n=s&&s.forcedJSONParsing,r=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(n&&!this.responseType||r)){const i=!(s&&s.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?Z.from(l,Z.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:xe.classes.FormData,Blob:xe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{ys.headers[e]={}});const Fu=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ou=e=>{const t={};let s,n,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),s=i.substring(0,r).trim().toLowerCase(),n=i.substring(r+1).trim(),!(!s||t[s]&&Fu[s])&&(s==="set-cookie"?t[s]?t[s].push(n):t[s]=[n]:t[s]=t[s]?t[s]+", "+n:n)}),t},to=Symbol("internals");function Qt(e){return e&&String(e).trim().toLowerCase()}function Fs(e){return e===!1||e==null?e:b.isArray(e)?e.map(Fs):String(e)}function Pu(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(e);)t[n[1]]=n[2];return t}const $u=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Cn(e,t,s,n,r){if(b.isFunction(n))return n.call(this,t,s);if(r&&(t=s),!!b.isString(t)){if(b.isString(n))return t.indexOf(n)!==-1;if(b.isRegExp(n))return n.test(t)}}function Mu(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,n)=>s.toUpperCase()+n)}function ku(e,t){const s=b.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+s,{value:function(r,o,i){return this[n].call(this,t,r,o,i)},configurable:!0})})}let Pe=class{constructor(t){t&&this.set(t)}set(t,s,n){const r=this;function o(l,a,u){const c=Qt(a);if(!c)throw new Error("header name must be a non-empty string");const d=b.findKey(r,c);(!d||r[d]===void 0||u===!0||u===void 0&&r[d]!==!1)&&(r[d||a]=Fs(l))}const i=(l,a)=>b.forEach(l,(u,c)=>o(u,c,a));if(b.isPlainObject(t)||t instanceof this.constructor)i(t,s);else if(b.isString(t)&&(t=t.trim())&&!$u(t))i(Ou(t),s);else if(b.isObject(t)&&b.isIterable(t)){let l={},a,u;for(const c of t){if(!b.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?b.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,s)}else t!=null&&o(s,t,n);return this}get(t,s){if(t=Qt(t),t){const n=b.findKey(this,t);if(n){const r=this[n];if(!s)return r;if(s===!0)return Pu(r);if(b.isFunction(s))return s.call(this,r,n);if(b.isRegExp(s))return s.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=Qt(t),t){const n=b.findKey(this,t);return!!(n&&this[n]!==void 0&&(!s||Cn(this,this[n],n,s)))}return!1}delete(t,s){const n=this;let r=!1;function o(i){if(i=Qt(i),i){const l=b.findKey(n,i);l&&(!s||Cn(n,n[l],l,s))&&(delete n[l],r=!0)}}return b.isArray(t)?t.forEach(o):o(t),r}clear(t){const s=Object.keys(this);let n=s.length,r=!1;for(;n--;){const o=s[n];(!t||Cn(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const s=this,n={};return b.forEach(this,(r,o)=>{const i=b.findKey(n,o);if(i){s[i]=Fs(r),delete s[o];return}const l=t?Mu(o):String(o).trim();l!==o&&delete s[o],s[l]=Fs(r),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return b.forEach(this,(n,r)=>{n!=null&&n!==!1&&(s[r]=t&&b.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const n=new this(t);return s.forEach(r=>n.set(r)),n}static accessor(t){const n=(this[to]=this[to]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=Qt(i);n[l]||(ku(r,i),n[l]=!0)}return b.isArray(t)?t.forEach(o):o(t),this}};Pe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(Pe.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[s]=n}}});b.freezeMethods(Pe);function Sn(e,t){const s=this||ys,n=t||s,r=Pe.from(n.headers);let o=n.data;return b.forEach(e,function(l){o=l.call(s,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function Ei(e){return!!(e&&e.__CANCEL__)}function Kt(e,t,s){Z.call(this,e??"canceled",Z.ERR_CANCELED,t,s),this.name="CanceledError"}b.inherits(Kt,Z,{__CANCEL__:!0});function Fi(e,t,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?e(s):t(new Z("Request failed with status code "+s.status,[Z.ERR_BAD_REQUEST,Z.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Lu(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Iu(e,t){e=e||10;const s=new Array(e),n=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=n[o];i||(i=u),s[r]=a,n[r]=u;let d=o,y=0;for(;d!==r;)y+=s[d++],d=d%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const m=c&&u-c;return m?Math.round(y*1e3/m):void 0}}function Nu(e,t){let s=0,n=1e3/t,r,o;const i=(u,c=Date.now())=>{s=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),d=c-s;d>=n?i(u,c):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},n-d)))},()=>r&&i(r)]}const js=(e,t,s=3)=>{let n=0;const r=Iu(50,250);return Nu(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-n,u=r(a),c=i<=l;n=i;const d={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(d)},s)},so=(e,t)=>{const s=e!=null;return[n=>t[0]({lengthComputable:s,total:e,loaded:n}),t[1]]},no=e=>(...t)=>b.asap(()=>e(...t)),Hu=xe.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,xe.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(xe.origin),xe.navigator&&/(msie|trident)/i.test(xe.navigator.userAgent)):()=>!0,Uu=xe.hasStandardBrowserEnv?{write(e,t,s,n,r,o){const i=[e+"="+encodeURIComponent(t)];b.isNumber(s)&&i.push("expires="+new Date(s).toGMTString()),b.isString(n)&&i.push("path="+n),b.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Du(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function ju(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Oi(e,t,s){let n=!Du(t);return e&&(n||s==!1)?ju(e,t):t}const ro=e=>e instanceof Pe?{...e}:e;function Rt(e,t){t=t||{};const s={};function n(u,c,d,y){return b.isPlainObject(u)&&b.isPlainObject(c)?b.merge.call({caseless:y},u,c):b.isPlainObject(c)?b.merge({},c):b.isArray(c)?c.slice():c}function r(u,c,d,y){if(b.isUndefined(c)){if(!b.isUndefined(u))return n(void 0,u,d,y)}else return n(u,c,d,y)}function o(u,c){if(!b.isUndefined(c))return n(void 0,c)}function i(u,c){if(b.isUndefined(c)){if(!b.isUndefined(u))return n(void 0,u)}else return n(void 0,c)}function l(u,c,d){if(d in t)return n(u,c);if(d in e)return n(void 0,u)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,d)=>r(ro(u),ro(c),d,!0)};return b.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=a[c]||r,y=d(e[c],t[c],c);b.isUndefined(y)&&d!==l||(s[c]=y)}),s}const Pi=e=>{const t=Rt({},e);let{data:s,withXSRFToken:n,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Pe.from(i),t.url=Ai(Oi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(b.isFormData(s)){if(xe.hasStandardBrowserEnv||xe.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(xe.hasStandardBrowserEnv&&(n&&b.isFunction(n)&&(n=n(t)),n||n!==!1&&Hu(t.url))){const u=r&&o&&Uu.read(o);u&&i.set(r,u)}return t},Bu=typeof XMLHttpRequest<"u",Vu=Bu&&function(e){return new Promise(function(s,n){const r=Pi(e);let o=r.data;const i=Pe.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=r,c,d,y,m,g;function C(){m&&m(),g&&g(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let w=new XMLHttpRequest;w.open(r.method.toUpperCase(),r.url,!0),w.timeout=r.timeout;function k(){if(!w)return;const K=Pe.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),X={data:!l||l==="text"||l==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:K,config:e,request:w};Fi(function(ue){s(ue),C()},function(ue){n(ue),C()},X),w=null}"onloadend"in w?w.onloadend=k:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(k)},w.onabort=function(){w&&(n(new Z("Request aborted",Z.ECONNABORTED,e,w)),w=null)},w.onerror=function(){n(new Z("Network Error",Z.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let I=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const X=r.transitional||Ri;r.timeoutErrorMessage&&(I=r.timeoutErrorMessage),n(new Z(I,X.clarifyTimeoutError?Z.ETIMEDOUT:Z.ECONNABORTED,e,w)),w=null},o===void 0&&i.setContentType(null),"setRequestHeader"in w&&b.forEach(i.toJSON(),function(I,X){w.setRequestHeader(X,I)}),b.isUndefined(r.withCredentials)||(w.withCredentials=!!r.withCredentials),l&&l!=="json"&&(w.responseType=r.responseType),u&&([y,g]=js(u,!0),w.addEventListener("progress",y)),a&&w.upload&&([d,m]=js(a),w.upload.addEventListener("progress",d),w.upload.addEventListener("loadend",m)),(r.cancelToken||r.signal)&&(c=K=>{w&&(n(!K||K.type?new Kt(null,e,w):K),w.abort(),w=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const D=Lu(r.url);if(D&&xe.protocols.indexOf(D)===-1){n(new Z("Unsupported protocol "+D+":",Z.ERR_BAD_REQUEST,e));return}w.send(o||null)})},qu=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let n=new AbortController,r;const o=function(u){if(!r){r=!0,l();const c=u instanceof Error?u:this.reason;n.abort(c instanceof Z?c:new Kt(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new Z(`timeout ${t} of ms exceeded`,Z.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=n;return a.unsubscribe=()=>b.asap(l),a}},zu=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let n=0,r;for(;n<s;)r=n+t,yield e.slice(n,r),n=r},Wu=async function*(e,t){for await(const s of Ku(e))yield*zu(s,t)},Ku=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:n}=await t.read();if(s)break;yield n}}finally{await t.cancel()}},oo=(e,t,s,n)=>{const r=Wu(e,t);let o=0,i,l=a=>{i||(i=!0,n&&n(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await r.next();if(u){l(),a.close();return}let d=c.byteLength;if(s){let y=o+=d;s(y)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},cn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",$i=cn&&typeof ReadableStream=="function",Ju=cn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Mi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Zu=$i&&Mi(()=>{let e=!1;const t=new Request(xe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),io=64*1024,jn=$i&&Mi(()=>b.isReadableStream(new Response("").body)),Bs={stream:jn&&(e=>e.body)};cn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Bs[t]&&(Bs[t]=b.isFunction(e[t])?s=>s[t]():(s,n)=>{throw new Z(`Response type '${t}' is not supported`,Z.ERR_NOT_SUPPORT,n)})})})(new Response);const Gu=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(xe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await Ju(e)).byteLength},Xu=async(e,t)=>{const s=b.toFiniteNumber(e.getContentLength());return s??Gu(t)},Yu=cn&&(async e=>{let{url:t,method:s,data:n,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:y}=Pi(e);u=u?(u+"").toLowerCase():"text";let m=qu([r,o&&o.toAbortSignal()],i),g;const C=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let w;try{if(a&&Zu&&s!=="get"&&s!=="head"&&(w=await Xu(c,n))!==0){let X=new Request(t,{method:"POST",body:n,duplex:"half"}),fe;if(b.isFormData(n)&&(fe=X.headers.get("content-type"))&&c.setContentType(fe),X.body){const[ue,U]=so(w,js(no(a)));n=oo(X.body,io,ue,U)}}b.isString(d)||(d=d?"include":"omit");const k="credentials"in Request.prototype;g=new Request(t,{...y,signal:m,method:s.toUpperCase(),headers:c.normalize().toJSON(),body:n,duplex:"half",credentials:k?d:void 0});let D=await fetch(g);const K=jn&&(u==="stream"||u==="response");if(jn&&(l||K&&C)){const X={};["status","statusText","headers"].forEach(v=>{X[v]=D[v]});const fe=b.toFiniteNumber(D.headers.get("content-length")),[ue,U]=l&&so(fe,js(no(l),!0))||[];D=new Response(oo(D.body,io,ue,()=>{U&&U(),C&&C()}),X)}u=u||"text";let I=await Bs[b.findKey(Bs,u)||"text"](D,e);return!K&&C&&C(),await new Promise((X,fe)=>{Fi(X,fe,{data:I,headers:Pe.from(D.headers),status:D.status,statusText:D.statusText,config:e,request:g})})}catch(k){throw C&&C(),k&&k.name==="TypeError"&&/Load failed|fetch/i.test(k.message)?Object.assign(new Z("Network Error",Z.ERR_NETWORK,e,g),{cause:k.cause||k}):Z.from(k,k&&k.code,e,g)}}),Bn={http:pu,xhr:Vu,fetch:Yu};b.forEach(Bn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const lo=e=>`- ${e}`,Qu=e=>b.isFunction(e)||e===null||e===!1,ki={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let s,n;const r={};for(let o=0;o<t;o++){s=e[o];let i;if(n=s,!Qu(s)&&(n=Bn[(i=String(s)).toLowerCase()],n===void 0))throw new Z(`Unknown adapter '${i}'`);if(n)break;r[i||"#"+o]=n}if(!n){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(lo).join(`
`):" "+lo(o[0]):"as no adapter specified";throw new Z("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:Bn};function An(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Kt(null,e)}function ao(e){return An(e),e.headers=Pe.from(e.headers),e.data=Sn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ki.getAdapter(e.adapter||ys.adapter)(e).then(function(n){return An(e),n.data=Sn.call(e,e.transformResponse,n),n.headers=Pe.from(n.headers),n},function(n){return Ei(n)||(An(e),n&&n.response&&(n.response.data=Sn.call(e,e.transformResponse,n.response),n.response.headers=Pe.from(n.response.headers))),Promise.reject(n)})}const Li="1.9.0",un={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{un[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const co={};un.transitional=function(t,s,n){function r(o,i){return"[Axios v"+Li+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,l)=>{if(t===!1)throw new Z(r(i," has been removed"+(s?" in "+s:"")),Z.ERR_DEPRECATED);return s&&!co[i]&&(co[i]=!0,console.warn(r(i," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(o,i,l):!0}};un.spelling=function(t){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function ef(e,t,s){if(typeof e!="object")throw new Z("options must be an object",Z.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let r=n.length;for(;r-- >0;){const o=n[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new Z("option "+o+" must be "+a,Z.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new Z("Unknown option "+o,Z.ERR_BAD_OPTION)}}const Os={assertOptions:ef,validators:un},Ze=Os.validators;let At=class{constructor(t){this.defaults=t||{},this.interceptors={request:new eo,response:new eo}}async request(t,s){try{return await this._request(t,s)}catch(n){if(n instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=Rt(this.defaults,s);const{transitional:n,paramsSerializer:r,headers:o}=s;n!==void 0&&Os.assertOptions(n,{silentJSONParsing:Ze.transitional(Ze.boolean),forcedJSONParsing:Ze.transitional(Ze.boolean),clarifyTimeoutError:Ze.transitional(Ze.boolean)},!1),r!=null&&(b.isFunction(r)?s.paramsSerializer={serialize:r}:Os.assertOptions(r,{encode:Ze.function,serialize:Ze.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Os.assertOptions(s,{baseUrl:Ze.spelling("baseURL"),withXsrfToken:Ze.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=o&&b.merge(o.common,o[s.method]);o&&b.forEach(["delete","get","head","post","put","patch","common"],g=>{delete o[g]}),s.headers=Pe.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(C){typeof C.runWhen=="function"&&C.runWhen(s)===!1||(a=a&&C.synchronous,l.unshift(C.fulfilled,C.rejected))});const u=[];this.interceptors.response.forEach(function(C){u.push(C.fulfilled,C.rejected)});let c,d=0,y;if(!a){const g=[ao.bind(this),void 0];for(g.unshift.apply(g,l),g.push.apply(g,u),y=g.length,c=Promise.resolve(s);d<y;)c=c.then(g[d++],g[d++]);return c}y=l.length;let m=s;for(d=0;d<y;){const g=l[d++],C=l[d++];try{m=g(m)}catch(w){C.call(this,w);break}}try{c=ao.call(this,m)}catch(g){return Promise.reject(g)}for(d=0,y=u.length;d<y;)c=c.then(u[d++],u[d++]);return c}getUri(t){t=Rt(this.defaults,t);const s=Oi(t.baseURL,t.url,t.allowAbsoluteUrls);return Ai(s,t.params,t.paramsSerializer)}};b.forEach(["delete","get","head","options"],function(t){At.prototype[t]=function(s,n){return this.request(Rt(n||{},{method:t,url:s,data:(n||{}).data}))}});b.forEach(["post","put","patch"],function(t){function s(n){return function(o,i,l){return this.request(Rt(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}At.prototype[t]=s(),At.prototype[t+"Form"]=s(!0)});let tf=class Ii{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(o){s=o});const n=this;this.promise.then(r=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](r);n._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{n.subscribe(l),o=l}).then(r);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,l){n.reason||(n.reason=new Kt(o,i,l),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=n=>{t.abort(n)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new Ii(function(r){t=r}),cancel:t}}};function sf(e){return function(s){return e.apply(null,s)}}function nf(e){return b.isObject(e)&&e.isAxiosError===!0}const Vn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Vn).forEach(([e,t])=>{Vn[t]=e});function Ni(e){const t=new At(e),s=pi(At.prototype.request,t);return b.extend(s,At.prototype,t,{allOwnKeys:!0}),b.extend(s,t,null,{allOwnKeys:!0}),s.create=function(r){return Ni(Rt(e,r))},s}const me=Ni(ys);me.Axios=At;me.CanceledError=Kt;me.CancelToken=tf;me.isCancel=Ei;me.VERSION=Li;me.toFormData=an;me.AxiosError=Z;me.Cancel=me.CanceledError;me.all=function(t){return Promise.all(t)};me.spread=sf;me.isAxiosError=nf;me.mergeConfig=Rt;me.AxiosHeaders=Pe;me.formToJSON=e=>Ti(b.isHTMLForm(e)?new FormData(e):e);me.getAdapter=ki.getAdapter;me.HttpStatusCode=Vn;me.default=me;const{Axios:Ap,AxiosError:Rp,CanceledError:Tp,isCancel:Ep,CancelToken:Fp,VERSION:Op,all:Pp,Cancel:$p,isAxiosError:Mp,spread:kp,toFormData:Lp,AxiosHeaders:Ip,HttpStatusCode:Np,formToJSON:Hp,getAdapter:Up,mergeConfig:Dp}=me,Hi={API_BASE_URL:"https://api.niswey.net/rmscrmseries/"},rf=Hi.API_BASE_URL,pt=me.create({baseURL:rf,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});pt.interceptors.request.use(e=>{var t;return console.log("Axios Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url,e.baseURL+e.url),e},e=>(console.error("Axios Request Error:",e),Promise.reject(e)));pt.interceptors.response.use(e=>e,e=>{var t,s;return console.error("Axios Response Error:",(t=e.response)==null?void 0:t.status,((s=e.response)==null?void 0:s.data)||e.message),Promise.reject(e)});function fn(){return{apiClient:pt,get:(e,t={})=>pt.get(e,t),post:(e,t={},s={})=>pt.post(e,t,s),put:(e,t={},s={})=>pt.put(e,t,s),delete:(e,t={})=>pt.delete(e,t),patch:(e,t={},s={})=>pt.patch(e,t,s)}}const of={class:"min-w-80 max-w-md w-full bg-white shadow-lg rounded-lg ring-1 ring-black ring-opacity-5 overflow-hidden"},lf={class:"p-4"},af={class:"flex items-start"},cf={class:"flex-shrink-0"},uf={key:0,class:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},ff={key:1,class:"h-6 w-6 text-red-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},df={key:2,class:"h-6 w-6 text-yellow-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},pf={key:3,class:"h-6 w-6 text-blue-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},hf={class:"ml-3 w-0 flex-1 pt-0.5"},mf={class:"text-sm font-medium text-gray-900"},gf={key:0,class:"mt-1 text-sm text-gray-500"},yf={key:0,class:"h-1 bg-gray-200"},bf={__name:"Toast",props:{type:{type:String,default:"info",validator:e=>["success","error","warning","info"].includes(e)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:5e3},autoClose:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:t}){const s=e,n=t,r=z(!1),o=z(100);let i=null,l=null;const a=()=>{r.value=!0,s.autoClose&&s.duration>0&&c()},u=()=>{r.value=!1,d(),setTimeout(()=>{n("close")},300)},c=()=>{d(),i=setTimeout(()=>{u()},s.duration);const y=100,g=100/(s.duration/y);l=setInterval(()=>{o.value-=g,o.value<=0&&clearInterval(l)},y)},d=()=>{i&&(clearTimeout(i),i=null),l&&(clearInterval(l),l=null)};return Ft(()=>{a()}),nr(()=>{d()}),(y,m)=>r.value?(N(),B("div",{key:0,class:_e(["transition-all duration-300 ease-in-out transform pointer-events-auto",r.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"])},[p("div",of,[p("div",lf,[p("div",af,[p("div",cf,[e.type==="success"?(N(),B("svg",uf,m[0]||(m[0]=[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):e.type==="error"?(N(),B("svg",ff,m[1]||(m[1]=[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):e.type==="warning"?(N(),B("svg",df,m[2]||(m[2]=[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)]))):(N(),B("svg",pf,m[3]||(m[3]=[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),p("div",hf,[p("p",mf,se(e.title),1),e.message?(N(),B("p",gf,se(e.message),1)):Ce("",!0)]),p("div",{class:"ml-4 flex-shrink-0 flex"},[p("button",{onClick:u,class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},m[4]||(m[4]=[p("span",{class:"sr-only"},"Close",-1),p("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[p("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))])])]),e.autoClose&&e.duration>0?(N(),B("div",yf,[p("div",{class:_e(["h-full transition-all duration-100 ease-linear",{"bg-green-500":e.type==="success","bg-red-500":e.type==="error","bg-yellow-500":e.type==="warning","bg-blue-500":e.type==="info"}]),style:Js({width:o.value+"%"})},null,6)])):Ce("",!0)])],2)):Ce("",!0)}},vf={class:"fixed top-4 right-4 z-50 space-y-3 pointer-events-none"},dn={__name:"ToastContainer",setup(e,{expose:t}){const s=z([]);let n=1;const r=l=>{const a={id:n++,type:l.type||"info",title:l.title,message:l.message||"",duration:l.duration||5e3,autoClose:l.autoClose!==!1};return s.value.push(a),s.value.length>5&&s.value.shift(),a.id},o=l=>{const a=s.value.findIndex(u=>u.id===l);a>-1&&s.value.splice(a,1)};return t({addToast:r,removeToast:o,clearAll:()=>{s.value=[]},success:(l,a,u={})=>r({...u,type:"success",title:l,message:a}),error:(l,a,u={})=>r({...u,type:"error",title:l,message:a}),warning:(l,a,u={})=>r({...u,type:"warning",title:l,message:a}),info:(l,a,u={})=>r({...u,type:"info",title:l,message:a})}),(l,a)=>(N(),B("div",vf,[(N(!0),B(de,null,Be(s.value,u=>(N(),Bt(bf,{key:u.id,type:u.type,title:u.title,message:u.message,duration:u.duration,"auto-close":u.autoClose,onClose:c=>o(u.id)},null,8,["type","title","message","duration","auto-close","onClose"]))),128))]))}},Pt=z(null);function Tt(){const e=l=>{Pt.value=l,console.log("Toast container set:",l)},t=l=>{if(console.log("Adding toast:",l,"Container:",Pt.value),!Pt.value){console.warn("Toast container not initialized, trying to show toast anyway");const a=`${l.title}: ${l.message}`;alert(a);return}return Pt.value.addToast(l)};return{setToastContainer:e,success:(l,a="",u={})=>t({...u,type:"success",title:l,message:a}),error:(l,a="",u={})=>t({...u,type:"error",title:l,message:a}),warning:(l,a="",u={})=>t({...u,type:"warning",title:l,message:a}),info:(l,a="",u={})=>t({...u,type:"info",title:l,message:a}),clearAll:()=>{Pt.value&&Pt.value.clearAll()}}}const xf={class:"flex flex-col min-h-screen bg-gray-50"},_f={class:"flex-1 flex flex-col overflow-hidden bg-white"},wf={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},Cf={key:0,class:"flex items-center justify-center py-16"},Sf={key:1,class:"flex items-center justify-center py-16"},Af={class:"col-span-4 flex items-center"},Rf={class:"min-w-0 flex-1"},Tf={class:"text-sm font-medium text-gray-900 truncate"},Ef={class:"text-xs text-gray-500 truncate"},Ff={class:"col-span-4 flex items-center"},Of={class:"min-w-0 flex-1"},Pf={class:"text-sm text-gray-900 truncate"},$f={class:"col-span-4 flex items-center"},Mf=["onUpdate:modelValue"],kf={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},Lf={class:"flex justify-end mt-6 px-6 py-4"},If={class:"flex items-center space-x-4"},Nf=["disabled"],Hf={__name:"StepUserMapping",emits:["next","back"],setup(e,{emit:t}){const s=Xs([]),n=z(!0),{get:r,post:o}=fn(),i=z(null),l=Tt(),a=t,u=$e(()=>s.filter(m=>m.status&&m.status!=="").length),c=$e(()=>s.length>0&&s.every(m=>m.status&&m.status!=="")),d=async()=>{n.value=!0;const m=new URLSearchParams(window.location.search).get("portal_id");if(!m){l.error("Missing Portal ID","Portal ID is required in the URL to proceed."),n.value=!1;return}try{const{data:g}=await r(`api/hubspot/users?portal_id=${m}`);g.ok&&Array.isArray(g.data)?s.splice(0,s.length,...g.data):console.error("Unexpected response format:",g)}catch(g){console.error("Error fetching users:",g),l.error("Fetch Failed","Failed to fetch users. Please try again.")}finally{n.value=!1}};Ft(async()=>{await zt(),console.log("StepUserMapping mounted, toastContainer.value:",i.value),i.value?(l.setToastContainer(i.value),console.log("Toast container initialized successfully")):console.error("Toast container not found!"),d()});const y=async()=>{if(console.log("validateAndContinue called, allUsersMapped:",c.value),!c.value){console.log("Showing warning toast.."),l.warning("Incomplete Mapping","Please select a user status for all users before proceeding.");return}try{const m=new URLSearchParams(window.location.search).get("portal_id");await o("api/hubspot/users/save",{users:s,portal_id:m}),console.log("Showing success toast..."),l.success("User Mappings Saved",`Successfully saved ${u.value} user mapping(s). Proceeding to next step.`),setTimeout(()=>{a("next")},1500)}catch(m){console.error("Error saving users:",m),console.log("Showing error toast..."),l.error("Save Failed","Failed to save user mappings. Please try again.")}};return(m,g)=>(N(),B("div",xf,[g[5]||(g[5]=p("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[p("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"User Mapping"),p("p",{class:"text-sm text-gray-600 leading-relaxed"},[Re(" RMS app integration will be available only for mapped HubSpot-RMS user accounts. "),p("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),p("div",_f,[g[3]||(g[3]=He('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-4">HubSpot Member</div><div class="col-span-4">Email ID</div><div class="col-span-4">User Status</div></div></div>',1)),p("div",wf,[n.value?(N(),B("div",Cf,g[0]||(g[0]=[He('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot Users...</p></div>',1)]))):!n.value&&(!s||s.length===0)?(N(),B("div",Sf,g[1]||(g[1]=[He('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No Users found from hubspot.</p></div>',1)]))):Ce("",!0),(N(!0),B(de,null,Be(s,(C,w)=>(N(),B("div",{key:w,class:_e(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":w===s.length-1}])},[p("div",Af,[p("div",Rf,[p("div",Tf,se(C.name),1),p("div",Ef,se(C.firstName)+" "+se(C.lastName),1)])]),p("div",Ff,[p("div",Of,[p("div",Pf,se(C.email),1)])]),p("div",$f,[St(p("select",{"onUpdate:modelValue":k=>s[w].status=k,class:"w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150"},g[2]||(g[2]=[p("option",{disabled:"",value:""},"Select user type...",-1),p("option",{value:"Admin"},"Admin",-1),p("option",{value:"User"},"User",-1)]),8,Mf),[[qt,s[w].status]])])],2))),128))])]),p("div",kf,[p("div",Lf,[p("div",If,[p("button",{class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",onClick:y,disabled:!c.value},g[4]||(g[4]=[Re(" Next "),p("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,Nf)])])]),pe(dn,{ref_key:"toastContainer",ref:i},null,512)]))}},Uf={key:0,class:"fixed inset-0 bg-black/30 flex items-center justify-center z-50"},Df={class:"bg-white rounded-xl p-6 w-full max-w-md shadow-lg relative"},jf={class:"mb-4"},Bf={class:"mb-6"},Vf=["value"],qf={class:"mb-6"},zf=["value"],Wf={key:0,class:"mb-4 p-3 rounded-lg"},Kf={key:0,class:"text-green-800 bg-green-100 border border-green-200 rounded-lg p-3"},Jf={class:"text-sm mt-1"},Zf={key:1,class:"text-red-800 bg-red-100 border border-red-200 rounded-lg p-3"},Gf={class:"text-sm mt-1"},Xf={class:"flex justify-end space-x-3"},Yf=["disabled"],fr={__name:"AddFieldModal",props:{show:Boolean,rmsOptions:{type:Array,default:()=>[]},hubspotOptions:{type:Array,default:()=>[]}},emits:["close","add"],setup(e,{emit:t}){const s=e,n=t,r=z(""),o=z(""),i=z("");Ss(()=>s.show,d=>{d&&(r.value="",o.value="",i.value="")});const l=$e(()=>{if(!o.value||!i.value)return!1;const d=o.value.field_type,y=i.value.type;return y==="number"?["integer","double","float","number"].includes(d):y==="bool"?["boolean","bool"].includes(d):y==="date"||y==="datetime"?["date","datetime"].includes(d):y==="string"?["string","text"].includes(d):d===y}),a=()=>{r.value="",o.value="",i.value=""},u=()=>{a(),n("close")},c=()=>{if(r.value&&o.value&&i.value){if(!l.value)return;n("add",{hubspotField:r.value,rmsField:o.value,hubspotFieldType:i.value}),u()}else return};return(d,y)=>e.show?(N(),B("div",Uf,[p("div",Df,[p("button",{onClick:u,class:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl"}," × "),y[11]||(y[11]=p("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Add HubSpot field",-1)),p("div",jf,[y[3]||(y[3]=p("label",{class:"block text-sm text-gray-700 mb-1"},"HubSpot fields",-1)),St(p("input",{type:"text","onUpdate:modelValue":y[0]||(y[0]=m=>r.value=m),placeholder:"Field of study",class:"w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[rc,r.value]])]),p("div",Bf,[y[5]||(y[5]=p("label",{class:"block text-sm text-gray-700 mb-1"},"Hubspot field Type",-1)),St(p("select",{"onUpdate:modelValue":y[1]||(y[1]=m=>i.value=m),class:"w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[y[4]||(y[4]=p("option",{disabled:"",value:""},"Select field type",-1)),(N(!0),B(de,null,Be(e.hubspotOptions,m=>(N(),B("option",{key:m,value:m},se(m.label),9,Vf))),128))],512),[[qt,i.value]])]),p("div",qf,[y[7]||(y[7]=p("label",{class:"block text-sm text-gray-700 mb-1"},"RMS fields",-1)),St(p("select",{"onUpdate:modelValue":y[2]||(y[2]=m=>o.value=m),class:"w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[y[6]||(y[6]=p("option",{disabled:"",value:""},"Select field",-1)),(N(!0),B(de,null,Be(e.rmsOptions,m=>(N(),B("option",{key:m.id,value:m},se(m.field_name)+" ("+se(m.field_type||m.type)+")",9,zf))),128))],512),[[qt,o.value]])]),o.value&&i.value?(N(),B("div",Wf,[l.value?(N(),B("div",Kf,[y[8]||(y[8]=p("div",{class:"flex items-center"},[p("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[p("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),p("span",{class:"font-medium"},"Field types match!")],-1)),p("p",Jf,"RMS: "+se(o.value.field_type||o.value.type)+" ↔ HubSpot: "+se(i.value.type),1)])):(N(),B("div",Zf,[y[9]||(y[9]=p("div",{class:"flex items-center"},[p("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[p("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})]),p("span",{class:"font-medium"},"Field types don't match!")],-1)),p("p",Gf,"RMS: "+se(o.value.field_type||o.value.type)+" ≠ HubSpot: "+se(i.value.type),1),y[10]||(y[10]=p("p",{class:"text-sm mt-1"},"Please select fields with matching types.",-1))]))])):Ce("",!0),p("div",Xf,[p("button",{onClick:u,class:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100"}," Cancel "),p("button",{onClick:c,disabled:!l.value||!r.value||!o.value||!i.value,class:_e(["px-4 py-2 rounded-lg text-white transition-colors duration-150",{"bg-blue-600 hover:bg-blue-700 cursor-pointer":l.value&&r.value&&o.value&&i.value,"bg-gray-400 cursor-not-allowed":!l.value||!r.value||!o.value||!i.value}])}," Add ",10,Yf)])])])):Ce("",!0)}},Qf={key:0,class:"fixed inset-0 bg-black/30 flex items-center justify-center z-50"},ed={class:"bg-white rounded-lg p-6 w-full max-w-md relative shadow-lg"},td={class:"text-sm text-gray-700 mb-6"},sd={class:"font-medium"},dr={__name:"ConfirmRemoveModal",props:{show:Boolean,type:{type:String,required:!0},fieldName:{type:String,required:!0}},emits:["close","confirm"],setup(e,{emit:t}){const s=t,n=()=>s("close"),r=()=>s("confirm");return(o,i)=>e.show?(N(),B("div",Qf,[p("div",ed,[p("button",{onClick:n,class:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl font-bold"}," × "),i[1]||(i[1]=p("h2",{class:"text-lg font-semibold text-gray-900 mb-2"}," Remove from mappings ",-1)),p("p",td,[i[0]||(i[0]=Re(" You are about to remove “")),p("span",sd,se(e.fieldName),1),Re("” field from "+se(e.type)+" field mappings. ",1)]),p("div",{class:"flex justify-end space-x-4"},[p("button",{onClick:n,class:"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100"}," Cancel "),p("button",{onClick:r,class:"bg-red-100 text-red-600 hover:bg-red-200 font-medium px-4 py-2 rounded-lg"}," Remove ")])])])):Ce("",!0)}},nd={class:"flex flex-col min-h-screen bg-gray-50"},rd={class:"flex-1 flex flex-col overflow-hidden bg-white"},od={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},id={key:0,class:"flex items-center justify-center py-16"},ld={key:1,class:"flex items-center justify-center py-16"},ad={class:"col-span-5 flex items-center"},cd={class:"min-w-0 flex-1"},ud={class:"text-sm font-medium text-gray-900 truncate"},fd={class:"text-xs text-gray-500 truncate"},dd={class:"col-span-5 flex items-center"},pd=["onUpdate:modelValue"],hd=["value"],md={class:"col-span-2 flex items-center justify-center"},gd=["onClick"],yd={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},bd={class:"flex justify-between mt-6 px-6 py-4"},vd={class:"flex space-x-4"},xd=["disabled"],_d={__name:"StepCompanyField",emits:["next","back"],setup(e,{emit:t}){const s=t,{get:n,post:r}=fn(),o=z(null),{success:i,error:l,warning:a}=Tt(),u=z(!1),c=z(!1),d=z([]),y=z([]),m=z([]),g=z([]),C=z(null),w=z(null),k=z(!0),D=$e(()=>Array.isArray(g.value)?g.value.filter(v=>v.field&&v.field!=="").length:0),K=$e(()=>{if(!Array.isArray(m.value)||!Array.isArray(g.value))return[];const v=g.value.filter(_=>_.field&&_.field!=="").map(_=>_.field.id);return m.value.filter(_=>!v.includes(_.id))}),I=v=>{if(!v||!v.type||!Array.isArray(m.value))return[];const _=g.value.filter(O=>O.field&&O.field!==""&&O.name!==v.name).map(O=>O.field.id);return m.value.filter(O=>{if(_.includes(O.id))return!1;const E=O.field_type,L=v.type;return L==="number"?["integer","double","float","number"].includes(E):L==="bool"?["boolean","bool"].includes(E):L==="date"||L==="datetime"?["date","datetime"].includes(E):L==="string"?["string","text"].includes(E):E===L})};Ft(async()=>{if(await zt(),o.value){const{setToastContainer:v}=Tt();v(o.value)}try{k.value=!0;const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required in the URL to proceed."),k.value=!1;return}const[_,O,E]=await Promise.all([n(`api/hubspot/company-fields?portal_id=${v}`),n("api/company/rms-fields"),n(`api/company/field-mapping?portal_id=${v}`)]);d.value=_.data.data||[],m.value=O.data.data||[];const L=E.data.data||[];if(Array.isArray(d.value)&&d.value.length>0){const H=new Map;d.value.forEach(q=>{if(q.fieldType&&q.type){const Q=`${q.fieldType}-${q.type}`;H.has(Q)||H.set(Q,{fieldType:q.fieldType,type:q.type,label:`${q.fieldType} (${q.type})`})}}),y.value=Array.from(H.values())}if(Array.isArray(d.value)&&d.value.length>0){const H=d.value.filter(q=>q.readOnlyValue===!1);g.value=H.map(q=>{const Q=L.find(ee=>ee.hubspot_field===q.name);let Y="";return Q&&(Y=m.value.find(ee=>ee.field_name===Q.rms_field)||""),{name:q.name,label:q.label,type:q.type,fieldType:q.fieldType,field:Y}})}else g.value=[]}catch(v){d.value=[],m.value=[],g.value=[],v("Failed to Load Data","Unable to load field data. Please refresh the page and try again.")}finally{k.value=!1}}),console.log("Company",g);const X=(v,_)=>{C.value=v,w.value=_,c.value=!0},fe=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to remove fields.");return}const _={portal_id:parseInt(v),name:C.value.name},O=await r("api/hubspot/remove-company-fields",_);O.data.ok?(g.value.splice(w.value,1),i("Field Removed",`HubSpot field "${C.value.label}" has been removed successfully.`)):l("Removal Failed",O.data.error||"Failed to remove HubSpot field. Please try again.")}catch(v){v("Removal Error","An error occurred while removing the HubSpot field. Please try again.")}c.value=!1,C.value=null,w.value=null},ue=async v=>{try{const _=new URLSearchParams(window.location.search).get("portal_id");if(!_){l("Missing Portal ID","Portal ID is required to create fields.");return}const O=v.hubspotField.toLowerCase().replace(/\s+/g,"_"),E=v.rmsField.field_name,L=v.hubspotFieldType||{},H=L.fieldType||"text",q=L.type||"string",Q={portal_id:parseInt(_),rms_field:E,data:{name:O,label:v.hubspotField,groupName:"companyinformation",type:q,fieldType:H,formField:!0}},Y=await r("api/hubspot/add-company-fields",Q);Y.data.ok?(g.value.push({name:O,label:v.hubspotField,type:q,fieldType:H,field:v.rmsField||""}),i("Field Created",`HubSpot field "${v.hubspotField}" has been created successfully!`)):l("Creation Failed",Y.data.error||"Failed to create HubSpot field. Please try again.")}catch(_){_("Creation Error","An error occurred while creating the HubSpot field. Please try again.")}u.value=!1},U=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to save field mappings.");return}const _=g.value.filter(L=>L.field&&L.field!=="");if(_.length===0){a("No Fields Mapped","Please map at least one field before proceeding to the next step.");return}const O={portal_id:parseInt(v),mappings:_.map(L=>({hubspot_field:L.name,rms_field:L.field.field_name}))},E=await r("api/company/fields-mapping",O);E.data.ok?(i("Mappings Saved",`Successfully saved ${_.length} company field mapping(s). Proceeding to next step.`),setTimeout(()=>{s("next")},1500)):l("Save Failed",E.data.error||"Failed to save field mappings. Please try again.")}catch(v){v("Save Error","An error occurred while saving field mappings. Please try again.")}};return(v,_)=>{var O;return N(),B("div",nd,[_[10]||(_[10]=p("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[p("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"Company Field Mapping"),p("p",{class:"text-sm text-gray-600 leading-relaxed"},[Re(" Mapping company data fields ensures that information seamlessly moves between RMS and HubSpot, maintaining accuracy and consistency for your HubSpot companies. "),p("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),p("div",rd,[_[8]||(_[8]=He('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-5">HubSpot Company Field</div><div class="col-span-5">RMS Field</div><div class="col-span-2 text-center">Action</div></div></div>',1)),p("div",od,[k.value?(N(),B("div",id,_[4]||(_[4]=[He('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot company fields...</p></div>',1)]))):!k.value&&(!g.value||g.value.length===0)?(N(),B("div",ld,_[5]||(_[5]=[He('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No HubSpot company fields found.</p></div>',1)]))):Ce("",!0),!k.value&&g.value&&g.value.length>0?(N(!0),B(de,{key:2},Be(g.value,(E,L)=>(N(),B("div",{key:E.name,class:_e(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":L===g.value.length-1}])},[p("div",ad,[p("div",cd,[p("div",ud,se(E.label),1),p("div",fd,se(E.name),1)])]),p("div",dd,[St(p("select",{"onUpdate:modelValue":H=>E.field=H,class:_e(["w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150",{"border-green-400 bg-green-50 text-green-800":E.field&&E.field!=="","border-gray-300":!E.field||E.field===""}])},[_[6]||(_[6]=p("option",{value:""},"Select RMS Field...",-1)),(N(!0),B(de,null,Be(I(E),H=>(N(),B("option",{key:H.id,value:H},se(H.field_name)+" ("+se(H.field_type||H.type)+")",9,hd))),128))],10,pd),[[qt,E.field]])]),p("div",md,[p("button",{class:"inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:H=>X(E,L),title:"Remove field mapping"},_[7]||(_[7]=[p("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"currentColor",class:"flex-shrink-0"},[p("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"})],-1)]),8,gd)])],2))),128)):Ce("",!0)])]),p("div",yd,[p("div",bd,[p("div",vd,[p("button",{class:"border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:_[0]||(_[0]=E=>v.$emit("back"))}," Back "),p("button",{class:"border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:_[1]||(_[1]=E=>u.value=!0)}," Add HubSpot field ")]),p("button",{class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",onClick:U,disabled:D.value===0},_[9]||(_[9]=[Re(" Next "),p("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,xd)])]),pe(fr,{show:u.value,rmsOptions:K.value,hubspotOptions:y.value,onClose:_[2]||(_[2]=E=>u.value=!1),onAdd:ue},null,8,["show","rmsOptions","hubspotOptions"]),pe(dr,{show:c.value,fieldName:((O=C.value)==null?void 0:O.label)||"",type:"company",onClose:_[3]||(_[3]=E=>c.value=!1),onConfirm:fe},null,8,["show","fieldName"]),pe(dn,{ref_key:"toastContainer",ref:o},null,512)])}}},wd={class:"flex flex-col min-h-screen bg-gray-50"},Cd={class:"flex-1 flex flex-col overflow-hidden bg-white"},Sd={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},Ad={key:0,class:"flex items-center justify-center py-16"},Rd={key:1,class:"flex items-center justify-center py-16"},Td={class:"col-span-5 flex items-center"},Ed={class:"min-w-0 flex-1"},Fd={class:"text-sm font-medium text-gray-900 truncate"},Od={class:"text-xs text-gray-500 truncate"},Pd={class:"col-span-5 flex items-center"},$d=["onUpdate:modelValue"],Md=["value"],kd={class:"col-span-2 flex items-center justify-center"},Ld=["onClick"],Id={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},Nd={class:"flex justify-between mt-6 px-6 py-4"},Hd={class:"flex space-x-4"},Ud=["disabled"],Dd={__name:"StepContactField",emits:["next","back"],setup(e,{emit:t}){const s=t,{get:n,post:r}=fn(),o=z(null),{success:i,error:l,warning:a}=Tt(),u=z(!1),c=z(!1),d=z([]),y=z([]),m=z([]),g=z([]),C=z(null),w=z(null),k=z(!0),D=$e(()=>Array.isArray(g.value)?g.value.filter(v=>v.field&&v.field!=="").length:0),K=$e(()=>{if(!Array.isArray(m.value)||!Array.isArray(g.value))return[];const v=g.value.filter(_=>_.field&&_.field!=="").map(_=>_.field.id);return m.value.filter(_=>!v.includes(_.id))}),I=v=>{if(!v||!v.type||!Array.isArray(m.value))return[];const _=g.value.filter(O=>O.field&&O.field!==""&&O.name!==v.name).map(O=>O.field.id);return m.value.filter(O=>{if(_.includes(O.id))return!1;const E=O.field_type,L=v.type;return L==="number"?["integer","double","float","number"].includes(E):L==="bool"?["boolean","bool"].includes(E):L==="date"||L==="datetime"?["date","datetime"].includes(E):L==="string"?["string","text"].includes(E):E===L})};Ft(async()=>{if(await zt(),o.value){const{setToastContainer:v}=Tt();v(o.value)}try{k.value=!0;const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required in the URL to proceed."),k.value=!1;return}const[_,O,E]=await Promise.all([n(`api/hubspot/contact-fields?portal_id=${v}`),n("api/contact/rms-fields"),n(`api/contact/field-mapping?portal_id=${v}`)]);d.value=_.data.data||[],m.value=O.data.data||[];const L=E.data.data||[];if(Array.isArray(d.value)&&d.value.length>0){const H=new Map;d.value.forEach(q=>{if(q.fieldType&&q.type){const Q=`${q.fieldType}-${q.type}`;H.has(Q)||H.set(Q,{fieldType:q.fieldType,type:q.type,label:`${q.fieldType} (${q.type})`})}}),y.value=Array.from(H.values())}if(Array.isArray(d.value)&&d.value.length>0){const H=d.value.filter(q=>q.readOnlyValue===!1);g.value=H.map(q=>{const Q=L.find(ee=>ee.hubspot_field===q.name);let Y="";return Q&&(Y=m.value.find(ee=>ee.field_name===Q.rms_field)||""),{name:q.name,label:q.label,type:q.type,fieldType:q.fieldType,field:Y}})}else g.value=[]}catch(v){d.value=[],m.value=[],g.value=[],v("Failed to Load Data","Unable to load contact field data. Please refresh the page and try again.")}finally{k.value=!1}});const X=(v,_)=>{C.value=v,w.value=_,c.value=!0},fe=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to remove fields.");return}const _={portal_id:parseInt(v),name:C.value.name},O=await r("api/hubspot/remove-contact-fields",_);O.data.ok?(g.value.splice(w.value,1),i("Field Removed",`HubSpot contact field "${C.value.label}" has been removed successfully.`)):l("Removal Failed",O.data.error||"Failed to remove HubSpot contact field. Please try again.")}catch(v){v("Removal Error","An error occurred while removing the HubSpot contact field. Please try again.")}c.value=!1,C.value=null,w.value=null},ue=async v=>{try{const _=new URLSearchParams(window.location.search).get("portal_id");if(!_){l("Missing Portal ID","Portal ID is required to create fields.");return}const O=v.hubspotField.toLowerCase().replace(/\s+/g,"_"),E=v.rmsField.field_name,L=v.hubspotFieldType||{},H=L.fieldType||"text",q=L.type||"string",Q={portal_id:parseInt(_),rms_field:E,data:{name:O,label:v.hubspotField,groupName:"contactinformation",type:q,fieldType:H,formField:!0}},Y=await r("api/hubspot/add-contact-fields",Q);Y.data.ok?(g.value.push({name:O,label:v.hubspotField,type:q,fieldType:H,field:v.rmsField||""}),i("Field Created",`HubSpot contact field "${v.hubspotField}" has been created successfully!`)):l("Creation Failed",Y.data.error||"Failed to create HubSpot contact field. Please try again.")}catch(_){_("Creation Error","An error occurred while creating the HubSpot contact field. Please try again.")}u.value=!1},U=async()=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){l("Missing Portal ID","Portal ID is required to save field mappings.");return}const _=g.value.filter(L=>L.field&&L.field!=="");if(_.length===0){a("No Fields Mapped","Please map at least one field before proceeding to the next step.");return}const O={portal_id:parseInt(v),mappings:_.map(L=>({hubspot_field:L.name,rms_field:L.field.field_name}))},E=await r("api/contact/fields-mapping",O);E.data.ok?(i("Mappings Saved",`Successfully saved ${_.length} contact field mapping(s). Proceeding to next step.`),setTimeout(()=>{s("next")},1500)):l("Save Failed",E.data.error||"Failed to save field mappings. Please try again.")}catch(v){v("Save Error","An error occurred while saving field mappings. Please try again.")}};return(v,_)=>{var O;return N(),B("div",wd,[_[10]||(_[10]=p("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[p("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"Contact Field Mapping"),p("p",{class:"text-sm text-gray-600 leading-relaxed"},[Re(" Mapping contact data fields ensures that information seamlessly moves between RMS and HubSpot, maintaining accuracy and consistency for your HubSpot contacts. "),p("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),p("div",Cd,[_[8]||(_[8]=He('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-5">HubSpot Contact Field</div><div class="col-span-5">RMS Field</div><div class="col-span-2 text-center">Action</div></div></div>',1)),p("div",Sd,[k.value?(N(),B("div",Ad,_[4]||(_[4]=[He('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot contact fields...</p></div>',1)]))):!k.value&&(!g.value||g.value.length===0)?(N(),B("div",Rd,_[5]||(_[5]=[He('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No HubSpot contact fields found.</p></div>',1)]))):Ce("",!0),!k.value&&g.value&&g.value.length>0?(N(!0),B(de,{key:2},Be(g.value,(E,L)=>(N(),B("div",{key:E.name,class:_e(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":L===g.value.length-1}])},[p("div",Td,[p("div",Ed,[p("div",Fd,se(E.label),1),p("div",Od,se(E.name),1)])]),p("div",Pd,[St(p("select",{"onUpdate:modelValue":H=>E.field=H,class:_e(["w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150",{"border-green-400 bg-green-50 text-green-800":E.field&&E.field!=="","border-gray-300":!E.field||E.field===""}])},[_[6]||(_[6]=p("option",{value:""},"Select RMS Field...",-1)),(N(!0),B(de,null,Be(I(E),H=>(N(),B("option",{key:H.id,value:H},se(H.field_name)+" ("+se(H.field_type||H.type)+")",9,Md))),128))],10,$d),[[qt,E.field]])]),p("div",kd,[p("button",{class:"inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:H=>X(E,L),title:"Remove field mapping"},_[7]||(_[7]=[p("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"currentColor",class:"flex-shrink-0"},[p("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"})],-1)]),8,Ld)])],2))),128)):Ce("",!0)])]),p("div",Id,[p("div",Nd,[p("div",Hd,[p("button",{class:"border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:_[0]||(_[0]=E=>v.$emit("back"))}," Back "),p("button",{class:"border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:_[1]||(_[1]=E=>u.value=!0)}," Add HubSpot field ")]),p("button",{class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",onClick:U,disabled:D.value===0},_[9]||(_[9]=[Re(" Next "),p("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,Ud)])]),pe(fr,{show:u.value,rmsOptions:K.value,hubspotOptions:y.value,onClose:_[2]||(_[2]=E=>u.value=!1),onAdd:ue},null,8,["show","rmsOptions","hubspotOptions"]),pe(dr,{show:c.value,fieldName:((O=C.value)==null?void 0:O.label)||"",type:"contact",onClose:_[3]||(_[3]=E=>c.value=!1),onConfirm:fe},null,8,["show","fieldName"]),pe(dn,{ref_key:"toastContainer",ref:o},null,512)])}}},jd={class:"flex flex-col min-h-screen bg-gray-50"},Bd={class:"flex-1 flex flex-col overflow-hidden bg-white"},Vd={class:"flex-1 overflow-y-auto pb-4",style:{"max-height":"calc(100vh - 220px)"}},qd={key:0,class:"flex items-center justify-center py-16"},zd={key:1,class:"flex items-center justify-center py-16"},Wd={class:"col-span-5 flex items-center"},Kd={class:"min-w-0 flex-1"},Jd={class:"text-sm font-medium text-gray-900 truncate"},Zd={class:"text-xs text-gray-500 truncate"},Gd={class:"col-span-5 flex items-center"},Xd=["onUpdate:modelValue"],Yd=["value"],Qd={class:"col-span-2 flex items-center justify-center"},ep=["onClick"],tp={class:"flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20"},sp={class:"flex justify-between mt-6 px-6 py-4"},np={class:"flex space-x-4"},rp=["disabled"],op={__name:"StepDealField",emits:["next","back"],setup(e,{emit:t}){const{get:s,post:n}=fn(),r=z(null),{success:o,error:i,warning:l}=Tt(),a=z(!1),u=z(!1),c=z([]),d=z([]),y=z([]),m=z([]),g=z(null),C=z(null),w=z(!0),k=$e(()=>Array.isArray(m.value)?m.value.filter(U=>U.field&&U.field!=="").length:0),D=$e(()=>{if(!Array.isArray(y.value)||!Array.isArray(m.value))return[];const U=m.value.filter(v=>v.field&&v.field!=="").map(v=>v.field.id);return y.value.filter(v=>!U.includes(v.id))}),K=U=>{if(!U||!U.type||!Array.isArray(y.value))return[];const v=m.value.filter(_=>_.field&&_.field!==""&&_.name!==U.name).map(_=>_.field.id);return y.value.filter(_=>{if(v.includes(_.id))return!1;const O=_.field_type,E=U.type;return E==="number"?["integer","double","float","number"].includes(O):E==="bool"?["boolean","bool"].includes(O):E==="date"||E==="datetime"?["date","datetime"].includes(O):E==="string"?["string","text"].includes(O):O===E})},I=(U,v)=>{g.value=U,C.value=v,u.value=!0},X=async()=>{try{const U=new URLSearchParams(window.location.search).get("portal_id");if(!U){i("Missing Portal ID","Portal ID is required to remove fields.");return}const v={portal_id:parseInt(U),name:g.value.name},_=await n("api/hubspot/remove-deal-fields",v);_.data.ok?(m.value.splice(C.value,1),o("Field Removed",`HubSpot deal field "${g.value.label}" has been removed successfully.`)):i("Removal Failed",_.data.error||"Failed to remove HubSpot deal field. Please try again.")}catch(U){U("Removal Error","An error occurred while removing the HubSpot deal field. Please try again.")}u.value=!1,g.value=null,C.value=null};Ft(async()=>{if(await zt(),r.value){const{setToastContainer:U}=Tt();U(r.value)}try{w.value=!0;const U=new URLSearchParams(window.location.search).get("portal_id");if(!U){i("Missing Portal ID","Portal ID is required in the URL to proceed."),w.value=!1;return}const[v,_,O]=await Promise.all([s(`api/hubspot/deal-fields?portal_id=${U}`),s("api/deal/rms-fields"),s(`api/deal/field-mapping?portal_id=${U}`)]);c.value=v.data.data||[],y.value=_.data.data||[];const E=O.data.data||[];if(Array.isArray(c.value)&&c.value.length>0){const L=new Map;c.value.forEach(H=>{if(H.fieldType&&H.type){const q=`${H.fieldType}-${H.type}`;L.has(q)||L.set(q,{fieldType:H.fieldType,type:H.type,label:`${H.fieldType} (${H.type})`})}}),d.value=Array.from(L.values())}if(Array.isArray(c.value)&&c.value.length>0){const L=c.value.filter(H=>H.readOnlyValue===!1);m.value=L.map(H=>{const q=E.find(Y=>Y.hubspot_field===H.name);let Q="";return q&&(Q=y.value.find(Y=>Y.field_name===q.rms_field)||""),{name:H.name,label:H.label,type:H.type,fieldType:H.fieldType,field:Q}})}else m.value=[]}catch(U){c.value=[],y.value=[],m.value=[],U("Failed to Load Data","Unable to load deal field data. Please refresh the page and try again.")}finally{w.value=!1}}),console.log("deals",m);const fe=async U=>{try{const v=new URLSearchParams(window.location.search).get("portal_id");if(!v){i("Missing Portal ID","Portal ID is required to create fields.");return}const _=U.hubspotField.toLowerCase().replace(/\s+/g,"_"),O=U.rmsField.field_name,E=U.hubspotFieldType||{},L=E.fieldType||"text",H=E.type||"string",q={portal_id:parseInt(v),rms_field:O,data:{name:_,label:U.hubspotField,groupName:"dealinformation",type:H,fieldType:L,formField:!0}},Q=await n("api/hubspot/add-deal-fields",q);Q.data.ok?(m.value.push({name:_,label:U.hubspotField,type:H,fieldType:L,field:U.rmsField||""}),o("Field Created",`HubSpot deal field "${U.hubspotField}" has been created successfully!`)):i("Creation Failed",Q.data.error||"Failed to create HubSpot deal field. Please try again.")}catch(v){v("Creation Error","An error occurred while creating the HubSpot deal field. Please try again.")}a.value=!1},ue=async()=>{try{const U=new URLSearchParams(window.location.search).get("portal_id");if(!U){i("Missing Portal ID","Portal ID is required to save field mappings.");return}const v=m.value.filter(E=>E.field&&E.field!=="");if(v.length===0){l("No Fields Mapped","Please map at least one field before finishing the setup.");return}const _={portal_id:parseInt(U),mappings:v.map(E=>({hubspot_field:E.name,rms_field:E.field.field_name}))},O=await n("api/deal/fields-mapping",_);O.data.ok?o("Setup Complete!",`Successfully saved ${v.length} deal field mapping(s). Your HubSpot integration setup is now complete!`):i("Save Failed",O.data.error||"Failed to save deal field mappings. Please try again.")}catch(U){U("Save Error","An error occurred while saving deal field mappings. Please try again.")}};return(U,v)=>{var _;return N(),B("div",jd,[v[9]||(v[9]=p("div",{class:"flex-shrink-0 p-6 bg-white border-b border-gray-200"},[p("h2",{class:"text-2xl font-bold text-gray-900 mb-3"},"Deal Field Mapping"),p("p",{class:"text-sm text-gray-600 leading-relaxed"},[Re(" Mapping Deal data fields ensures that information seamlessly moves between RMS and HubSpot, maintaining accuracy and consistency for your HubSpot deal. "),p("a",{href:"#",class:"text-blue-600 hover:text-blue-800 underline"},"Learn more")])],-1)),p("div",Bd,[v[8]||(v[8]=He('<div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200"><div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900"><div class="col-span-5">HubSpot Deal Field</div><div class="col-span-5">RMS Field</div><div class="col-span-2 text-center">Action</div></div></div>',1)),p("div",Vd,[w.value?(N(),B("div",qd,v[4]||(v[4]=[He('<div class="text-center"><svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3><p class="mt-1 text-sm text-gray-500">Fetching HubSpot deal fields...</p></div>',1)]))):!w.value&&(!m.value||m.value.length===0)?(N(),B("div",zd,v[5]||(v[5]=[He('<div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3><p class="mt-1 text-sm text-gray-500">No HubSpot deal fields found.</p></div>',1)]))):Ce("",!0),!w.value&&m.value&&m.value.length>0?(N(!0),B(de,{key:2},Be(m.value,(O,E)=>(N(),B("div",{key:O.name,class:_e(["grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",{"mb-4":E===m.value.length-1}])},[p("div",Wd,[p("div",Kd,[p("div",Jd,se(O.label),1),p("div",Zd,se(O.name),1)])]),p("div",Gd,[St(p("select",{"onUpdate:modelValue":L=>O.field=L,class:_e(["w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-colors duration-150",{"border-green-400 bg-green-50 text-green-800":O.field&&O.field!=="","border-gray-300":!O.field||O.field===""}])},[v[6]||(v[6]=p("option",{value:""},"Select RMS Field...",-1)),(N(!0),B(de,null,Be(K(O),L=>(N(),B("option",{key:L.id,value:L},se(L.field_name)+" ("+se(L.field_type||L.type)+")",9,Yd))),128))],10,Xd),[[qt,O.field]])]),p("div",Qd,[p("button",{class:"inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:L=>I(O,E),title:"Remove field mapping"},v[7]||(v[7]=[p("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"currentColor",class:"flex-shrink-0"},[p("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.6665 2.49935C6.6665 2.03911 7.0396 1.66602 7.49984 1.66602H12.4998C12.9601 1.66602 13.3332 2.03911 13.3332 2.49935C13.3332 2.95959 12.9601 3.33268 12.4998 3.33268H7.49984C7.0396 3.33268 6.6665 2.95959 6.6665 2.49935ZM4.16004 4.16602H2.49984C2.0396 4.16602 1.6665 4.53911 1.6665 4.99935C1.6665 5.45959 2.0396 5.83268 2.49984 5.83268H3.38688L3.92162 13.8539C3.96358 14.4834 3.9983 15.0044 4.06056 15.4284C4.12539 15.8699 4.22821 16.2734 4.44241 16.6494C4.77586 17.2347 5.27883 17.7052 5.88503 17.999C6.27444 18.1877 6.68383 18.2635 7.12866 18.2988C7.55591 18.3327 8.07803 18.3327 8.70895 18.3327H11.2907C11.9216 18.3327 12.4438 18.3327 12.871 18.2988C13.3158 18.2635 13.7252 18.1877 14.1146 17.999C14.7208 17.7052 15.2238 17.2347 15.5573 16.6494C15.7715 16.2734 15.8743 15.8699 15.9391 15.4284C16.0014 15.0043 16.0361 14.4833 16.0781 13.8538L16.6128 5.83268H17.4998C17.9601 5.83268 18.3332 5.45959 18.3332 4.99935C18.3332 4.53911 17.9601 4.16602 17.4998 4.16602H15.8396C15.8348 4.16597 15.8299 4.16597 15.8251 4.16602H4.17462C4.16977 4.16597 4.16491 4.16597 4.16004 4.16602ZM14.9424 5.83268H5.05724L5.5824 13.71C5.62712 14.3808 5.65804 14.8355 5.70955 15.1863C5.75958 15.5271 5.82071 15.7017 5.89057 15.8244C6.05729 16.117 6.30877 16.3523 6.61188 16.4992C6.73887 16.5607 6.91722 16.6101 7.26055 16.6373C7.61403 16.6654 8.06972 16.666 8.74205 16.666H11.2576C11.93 16.666 12.3856 16.6654 12.7391 16.6373C13.0825 16.6101 13.2608 16.5607 13.3878 16.4992C13.6909 16.3523 13.9424 16.117 14.1091 15.8244C14.179 15.7017 14.2401 15.5271 14.2901 15.1863C14.3416 14.8355 14.3726 14.3808 14.4173 13.71L14.9424 5.83268ZM8.33317 7.91602C8.79341 7.91602 9.1665 8.28911 9.1665 8.74935V12.916C9.1665 13.3763 8.79341 13.7493 8.33317 13.7493C7.87293 13.7493 7.49984 13.3763 7.49984 12.916V8.74935C7.49984 8.28911 7.87293 7.91602 8.33317 7.91602ZM11.6665 7.91602C12.1267 7.91602 12.4998 8.28911 12.4998 8.74935V12.916C12.4998 13.3763 12.1267 13.7493 11.6665 13.7493C11.2063 13.7493 10.8332 13.3763 10.8332 12.916V8.74935C10.8332 8.28911 11.2063 7.91602 11.6665 7.91602Z"})],-1)]),8,ep)])],2))),128)):Ce("",!0)])]),p("div",tp,[p("div",sp,[p("div",np,[p("button",{class:"border border-gray-300 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:v[0]||(v[0]=O=>U.$emit("back"))}," Back "),p("button",{class:"border border-gray-300 hover:border-blue-700 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100",onClick:v[1]||(v[1]=O=>a.value=!0)}," Add HubSpot field ")]),p("button",{class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",onClick:ue,disabled:k.value===0}," Finish ",8,rp)])]),pe(fr,{show:a.value,hubspotOptions:d.value,rmsOptions:D.value,onClose:v[2]||(v[2]=O=>a.value=!1),onAdd:fe},null,8,["show","hubspotOptions","rmsOptions"]),pe(dr,{show:u.value,fieldName:((_=g.value)==null?void 0:_.label)||"",type:"deal",onClose:v[3]||(v[3]=O=>u.value=!1),onConfirm:X},null,8,["show","fieldName"]),pe(dn,{ref_key:"toastContainer",ref:r},null,512)])}}},Rn=Hi.API_BASE_URL;function ip(){const e=z(!1),t=z(null),s=i=>{if(i.startsWith("http"))return i;const l=i.startsWith("/")?i.slice(1):i;return`${Rn.endsWith("/")?Rn:`${Rn}/`}${l}`},n=async(i,l={})=>{e.value=!0,t.value=null;try{const a=s(i);console.log("API Call:",a);const u=await fetch(a,{headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest",...l.headers},...l}),c=await u.json();if(!u.ok)throw new Error(c.message||`HTTP error! status: ${u.status}`);return c}catch(a){throw t.value=a.message,console.error("API Error:",a),a}finally{e.value=!1}};return{loading:e,error:t,get:async(i,l={})=>{try{const a=new URLSearchParams(l),u=a.toString()?`${i}?${a}`:i,c=await n(u,{method:"GET"});return{ok:!0,data:c.data||c,hasAccess:c.hasAccess||!1}}catch(a){return{ok:!1,error:a.message}}},post:(i,l={})=>n(i,{method:"POST",body:JSON.stringify(l)}),apiCall:n}}const tt=z(null),$t=z(!1),ct=z(!1),Tn=z(!1),es=z(null);function lp(){const e=hc(),{get:t}=ip(),s=$e(()=>{var c;return((c=tt.value)==null?void 0:c.role)==="Admin"}),n=$e(()=>$t.value&&ct.value),r=()=>{const c=new URLSearchParams(window.location.search);return{portal_id:c.get("portal_id"),user_id:c.get("user_id"),email:c.get("email")}},o=async()=>{Tn.value=!0,es.value=null;try{const c=r();if(!c.portal_id||!c.user_id)throw new Error("Missing required authentication parameters");console.log("Checking user access with params:",{portal_id:c.portal_id,user_id:c.user_id?"present":"missing",email:c.email});const d=await t("api/user",{portal_id:c.portal_id,user_id:c.user_id});if(d.ok)return tt.value=d.data,$t.value=!0,ct.value=d.hasAccess,console.log("User authentication result:",{email:tt.value.email,role:tt.value.role,hasAccess:ct.value}),{success:!0,user:tt.value,hasAccess:ct.value};throw new Error(d.error||"Authentication failed")}catch(c){return console.error("Authentication error:",c),es.value=c.message,tt.value=null,$t.value=!1,ct.value=!1,{success:!1,error:c.message}}finally{Tn.value=!1}},i=()=>{e.push("/error?message=Access denied. Admin role required to access the wizard.")},l=()=>{e.push("/auth")};return{user:tt,isAuthenticated:$t,hasAdminAccess:ct,authLoading:Tn,authError:es,isAdmin:s,canAccessWizard:n,checkUserAccess:o,redirectToAccessDenied:i,redirectToAuth:l,logout:()=>{tt.value=null,$t.value=!1,ct.value=!1,es.value=null,l()},clearAuth:()=>{tt.value=null,$t.value=!1,ct.value=!1,es.value=null},getUrlParams:r}}const ap={key:0,class:"p-6 max-w-5xl mx-auto"},cp={key:1,class:"p-6 max-w-5xl mx-auto"},up={class:"text-center"},fp={class:"bg-red-50 border border-red-200 rounded-lg p-6"},dp={class:"text-red-700 mb-4"},pp={key:0},hp={key:0,class:"text-sm text-red-600 mb-4"},mp={key:2,class:"p-6 max-w-5xl w-full mx-auto"},gp={class:"flex space-x-8"},yp={class:"w-1/5 relative"},bp={class:"text-sm font-medium relative"},vp={class:"flex flex-col items-center z-0 mt-1"},xp={class:"flex-1 bg-white rounded-lg shadow p-6 transition-all duration-300"},_p={__name:"Wizard",setup(e){const{user:t,authLoading:s,authError:n,canAccessWizard:r,checkUserAccess:o,redirectToAuth:i}=lp();Ft(async()=>{console.log("Wizard component mounted, checking user access...");const C=await o();C.success?C.hasAccess?console.log("User has admin access, proceeding with wizard"):console.warn("User authenticated but lacks admin access"):console.error("Authentication failed:",C.error)});const l=()=>{i()},a=[{label:"Authorization"},{label:"User mapping",component:Hf},{label:"Company field mapping",component:_d},{label:"Contact field mapping",component:Dd},{label:"Deal field mapping",component:op}],u=z(1),c=$e(()=>u.value===a.length-1),d=z({users:[],company:{},contact:{},deal:{}}),y=()=>{u.value<a.length-1&&u.value++},m=()=>{u.value>0&&u.value--},g=()=>{console.log("Final Submission:",d.value)};return(C,w)=>(N(),Bt(Tc,null,{default:Uo(()=>[ut(s)?(N(),B("div",ap,w[1]||(w[1]=[p("div",{class:"text-center"},[p("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),p("p",{class:"text-gray-600"},"Checking access permissions...")],-1)]))):!ut(r)&&!ut(s)?(N(),B("div",cp,[p("div",up,[p("div",fp,[w[4]||(w[4]=p("div",{class:"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full"},[p("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),w[5]||(w[5]=p("h3",{class:"text-lg font-semibold text-red-800 mb-2"},"Access Denied",-1)),p("p",dp,[w[3]||(w[3]=Re(" You need Admin role to access the wizard. ")),ut(t)?(N(),B("span",pp,[w[2]||(w[2]=Re("Your current role is: ")),p("strong",null,se(ut(t).role||"Not assigned"),1)])):Ce("",!0)]),ut(n)?(N(),B("p",hp,se(ut(n)),1)):Ce("",!0),p("button",{onClick:l,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition"}," Go to Authorization ")])])])):(N(),B("div",mp,[w[6]||(w[6]=p("div",{class:"text-center mb-8"},[p("img",{src:uc,alt:"Niswey HubSpot Integration",class:"mx-auto h-12 mb-2"}),p("h2",{class:"text-xl font-semibold text-gray-700"},[Re(" Connecting "),p("span",{class:"font-bold text-gray-600"},"RMS"),Re(" to "),p("span",{class:"text-gray-600"},"HubSpot")])],-1)),p("div",gp,[p("div",yp,[p("ul",bp,[(N(),B(de,null,Be(a,(k,D)=>p("li",{key:D,class:"relative flex space-x-2"},[p("div",vp,[p("div",{class:_e(["w-3 h-3 rounded-full",[u.value>=D?"bg-blue-600":"bg-gray-300"]])},null,2),D<a.length-1?(N(),B("div",{key:0,class:_e(["h-8 w-1",u.value>D?"bg-blue-600":"bg-gray-300"])},null,2)):Ce("",!0)]),p("div",{class:_e(u.value===D?"text-blue-600 font-semibold":"text-gray-500")},se(k.label),3)])),64))])]),p("div",xp,[(N(),Bt(Wl(a[u.value].component),{modelValue:d.value[a[u.value].key],"onUpdate:modelValue":w[0]||(w[0]=k=>d.value[a[u.value].key]=k),onNext:y,onBack:m,"is-final":c.value,onSubmit:g},null,40,["modelValue","is-final"]))])])]))]),_:1}))}};lc(_p).mount("#app");
