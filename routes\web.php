<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CrmAppController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('app');
});

Route::get('hubauth', 'HubSpotController@authenticate');
Route::get('auth', 'HubSpotController@callback');

Route::get('test', 'CrmController@test');
Route::post('gettoken', 'Crm<PERSON>ontroller@getToken');
Route::post('webhook', 'CrmController@webhook');
Route::get('import', 'CrmController@csvToArray');

Route::get('logs', '\Rap2hpoutre\LaravelLogViewer\LogViewerController@index');

// UI Dashboards
Route::get('/initiate', [CrmAppController::class, 'initiate']);

Route::get('/{any}', function () {
    return view('app');
})->where('any', '^(?!api).*$');