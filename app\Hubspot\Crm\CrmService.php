<?php

namespace App\Hubspot\Crm;

use App\Models\User;
use App\Helpers\Func;
use App\Models\Account;
use App\Models\PortalTokens;

class CrmService
{
    protected $delimiter;

    protected $responseBuilder;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->delimiter = config('app.custom.delimiter');
        $this->responseBuilder = new ResponseBuilder($this->delimiter);
    }

    /**
     * Main entry point for the CRM service
     * Handles the logic previously in the controller
     *
     * @param  array  $input  The request input
     * @param  string  $phone  The parsed phone number
     * @param  string  $objectId  The HubSpot object ID
     * @return array Response for the controller to return as JSON
     */
    public function handleRequest(array $input, string $phone, string $objectId)
    {
        // Set the object ID for the response builder
        $this->responseBuilder->setObjectId($objectId);

        // Create request data object to reduce parameter passing
        $requestData = (object) [
            'input' => $input,
            'phone' => $phone,
            // 'instagram' => Func::checkInstaIdExists($input['portalId']),
            'portals' => PortalTokens::where(['portal_id' => $input['portalId']])->get(),
        ];

        // Handle inactive portal
        if ($requestData->portals->isEmpty() && $requestData->instagram->status == 'error') {
            return $this->notActiveResponse();
        }

        // Build the response
        return $this->buildResponse($requestData);
    }

    /**
     * Response for inactive portal
     *
     * @return array Response data
     */
    public function notActiveResponse()
    {
        return $this->responseBuilder
            ->reset()
            ->withTitle('Portal not Activated')
            ->withProperty('Contact support to activate your Account', '<EMAIL>')
            ->build();
    }

    /**
     * Response for no access
     *
     * @return array Response data
     */
    public function noAccessResponse()
    {
        return $this->responseBuilder
            ->reset()
            ->withTitle("You don't have access to this contact")
            ->withProperty('Contact your account admin for access', 'for more help, contact: <EMAIL>')
            ->build();
    }

    /**
     * Builds a response based on request data
     *
     * @param  object  $requestData  Object containing input, phone, portals, instagram
     * @return array The built response
     */
    public function buildResponse($requestData)
    {
        $builder = $this->responseBuilder->reset();

        // Create a context object with all necessary data
        $context = [
            'input' => $requestData->input,
            'phone' => $requestData->phone,
        ];

        $admin = null;
        // Add portal results if any exist
        if ($requestData->portals->isNotEmpty()) {
            foreach ($requestData->portals as $portal) {
                if ($admin === null) {
                    $admin = $this->checkAdmin($portal,$requestData->input['userEmail']);
                }
                
                if($admin){
                    $context['userHasAccess'] = true;
                } else {
                    $context['userHasAccess'] = false;
                }

                // if ($admin && $admin->user != $requestData->input['userEmail']) {
                //     ! isset($context['adminEmail']) && ($context['adminEmail'] = $admin->user);
                //     $context['userHasAccess'] = $this->checkAccess($portal, $requestData->input['userEmail']);
                // } else {
                //     $context['userHasAccess'] = true;
                // }
                $builder->addPortalResult($portal, $context);
            }
        }

        // Add Instagram results if available
        // if ($requestData->instagram && $requestData->instagram->status === 'ok') {
        //     $queryInput = $builder->prepareQueryInput($context['input'], $context['phone']);
        //     $builder->addInstagramResults($requestData->instagram, $queryInput);
        // }

        return $builder->build();
    }

    public function checkAdmin($portal,$userEmail)
    {
        return User::where('portal_id', $portal->portal_id)->where('email', $userEmail)
            ->where('role', 'Admin')
            ->exists();
    }

    // public function checkAccess($portal, $userEmail)
    // {
    //     return User::where([
    //         'account_id' => $portal->id,
    //         'email' => $userEmail,
    //         'approved' => 1,
    //         'active' => 1,
    //     ])->exists();
    // }
}
