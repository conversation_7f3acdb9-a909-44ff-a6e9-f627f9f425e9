<?php

if(env('APP_ENV') == 'local')
{
	// http://localhost:8000

	// return [
	// 	'current' => [
	// 		'url' => 'https://api.hubapi.com/integrations/v1/13113908/',
	// 		'scopes' => 'crm.schemas.companies.write%20crm.schemas.contacts.write%20crm.schemas.deals.read%20crm.schemas.deals.write%20oauth%20crm.objects.owners.read%20crm.objects.users.read%20crm.objects.contacts.write%20crm.objects.users.write%20crm.objects.companies.write%20crm.objects.companies.read%20crm.objects.deals.read%20crm.schemas.contacts.read%20crm.objects.deals.write%20crm.objects.contacts.read%20crm.schemas.companies.read',
	// 		'auth' => [
	// 			"grant_type" => "authorization_code",
	// 			"client_id" => "dd2d74d6-14f5-472e-96ed-45d01495d339",
	// 			"client_secret" => "987dcb1f-a0ec-4b83-bbbb-63621b02faf1",
	// 			"redirect_uri" => "http://localhost:8000/auth",
	// 			"code" => 13113908
	// 		]
	// 	]
	// ];
	// Project Credentail
	return [
		'current' => [
			'url' => 'https://api.hubapi.com/integrations/v1/13764349/',
			'scopes' => 'crm.schemas.companies.write%20crm.schemas.contacts.write%20crm.schemas.deals.read%20crm.schemas.deals.write%20oauth%20crm.objects.owners.read%20crm.objects.users.read%20crm.objects.contacts.write%20crm.objects.users.write%20crm.objects.companies.write%20crm.objects.companies.read%20crm.objects.deals.read%20crm.schemas.contacts.read%20crm.objects.deals.write%20crm.objects.contacts.read%20crm.schemas.companies.read',
			'auth' => [
				"grant_type" => "authorization_code",
				"client_id" => "799a73f0-3586-4312-ac8c-6adadeea04fb",
				"client_secret" => "22b6cbb2-1c77-4a8f-a2ad-d7900757423f",
				"redirect_uri" => "http://localhost:8000/auth",
				"code" => 13764349
			]
		]
	];
}

if(env('APP_ENV') == 'dev')
{
	// http://localhost:8000

	return [
		'current' => [
			'url' => 'https://api.hubapi.com/integrations/v1/13113908/',
			'scopes' => 'crm.schemas.companies.write%20crm.schemas.contacts.write%20crm.schemas.deals.read%20crm.schemas.deals.write%20oauth%20crm.objects.owners.read%20crm.objects.users.read%20crm.objects.contacts.write%20crm.objects.users.write%20crm.objects.companies.write%20crm.objects.companies.read%20crm.objects.deals.read%20crm.schemas.contacts.read%20crm.objects.deals.write%20crm.objects.contacts.read%20crm.schemas.companies.read',
			'auth' => [
				"grant_type" => "authorization_code",
				"client_id" => "dd2d74d6-14f5-472e-96ed-45d01495d339",
				"client_secret" => "987dcb1f-a0ec-4b83-bbbb-63621b02faf1",
				"redirect_uri" => "https://api.niswey.net/rmscrmseries/auth",
				"code" => 13113908
			]
		]
	];
}

// return [
//     "netcore" => [
//         "scope" => "contacts%20automation%20timeline%20oauth%20crm.objects.contacts.read%20crm.objects.contacts.write",
//         "url" => "https://api.hubapi.com/integrations/v1/286069/",
//         "auth" => [
//             "grant_type" => "authorization_code",
//             "client_id" => "68dac38c-9a8a-4881-a224-4feca89ae5d3",
//             "client_secret" => "c4aa8b1b-d8ab-40e9-9eea-d13d32afc42e",
//             "redirect_uri" => "https://dhi.niswey.net/auth"
//         ],
//         "config" => [
//             "app_id" => 286069,
//             "message_sent" => 1054471,
//         ],
//     ],
// ];
