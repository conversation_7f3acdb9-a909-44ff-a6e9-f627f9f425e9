<?php
// this is bound with Hubchat integration app
namespace App\Helpers;

use Log;
use Exception;
use App\Models\Messages;
use App\Helpers\Func;
use App\Models\Properties;
use App\Models\PortalTokens;
use Illuminate\Support\Facades\Http;

class Hubspot
{

	static function createTokens($code)
	{
		$hsApp = config('hsapp.current.auth');
		$hsApp['code'] = $code;
		$hsApp['grant_type'] = 'authorization_code';

		$tokens = Func::request('POST', 'https://api.hubapi.com/oauth/v1/token', [
			'form_params' => $hsApp
		]);

		return $tokens;
	}

	static function refreshToken($tokens)
	{
		Log::info("refreshToken called");
		$hsApp = config('hsapp.current.auth');
		$hsApp['grant_type'] = 'refresh_token';
		$hsApp['refresh_token'] = $tokens['refresh_token'];
		unset($hsApp['redirect_uri']);
		unset($hsApp['code']);

		$newTokens = Func::request(
			'POST',
			'https://api.hubapi.com/oauth/v1/token',
			['form_params' => $hsApp]
		);
		Log::info("[Hubspot:refreshToken] response: ".json_encode($newTokens));

		// $newTokens = Func::hubspotRequest('POST','https://api.hubapi.com/oauth/v1/token', $hsApp, [
		//     'Content-Type: application/x-www-form-urlencoded',
		// ]);

		if (isset($newTokens->status)) {
			Log::error("[Hubspot:refreshToken] Error for $tokens[portal_id] payload:\n " . json_encode($newTokens));
		}
		if (isset($newTokens->access_token)) {
			PortalTokens::where('portal_id', $tokens['portal_id'])->update(['access_token' => $newTokens->access_token, 'updated_at' => date("Y-m-d H:i:s")]);
			$tokens['access_token'] = $newTokens->access_token;
		}
		return $tokens;
	}

	static function getHubDomain($access_token)
	{
		return Func::request('GET', 'https://api.hubapi.com/oauth/v1/access-tokens/' . $access_token);
	}
	static function getTokens($portal_id)
	{
		// Log::info("getTokens called: ".$portal_id);
		$tokens = PortalTokens::where('portal_id', $portal_id)->first()->toArray();
		
		$updated = \Carbon\Carbon::parse($tokens['updated_at']);
		$expiresAt = $updated->copy()->addSeconds($tokens['expires_in']);
		$gracePeriod = 10; // seconds before expiry
		
		$shouldRefresh = now()->addSeconds($gracePeriod)->gte($expiresAt);

    
		if ($shouldRefresh) {
			Log::info("$portal_id: Token expired or about to expire — refreshing...");
			$tokens = self::refreshToken($tokens);
		} else {
			Log::info("$portal_id: Token still valid");
		}

		// $passed_time = time() - strtotime($tokens['updated_at']);
		
		// $expiredIn = $tokens['expires_in'] - 10; // refresh 10 seconds early to avoid having bad token during execution
		// $tokens = $passed_time >= $expiredIn ? self::refreshToken($tokens) : $tokens;

		// $tokens = $passed_time > 21600 ? self::refreshToken($tokens) : $tokens;
		// if($portal_id == 2720327) {
		// 	Log::info("[Hubspot:getTokens] tokens: ".json_encode($tokens));
		// }
		return $tokens;
	}

	static function getUserInfoByPortalId($access_token)
	{
		Log::info("[HS]getUserInfoByPortalId: Access Token: " . $access_token);
		$hub_url = 'https://api.hubapi.com/oauth/v1/access-tokens/' . $access_token;

		$response = Func::request('GET', $hub_url);
		if (!isset($response->user)) {
			Log::error("[HS]getUserInfoByPortalId: Response not found: " . $access_token);

		}
		Log::info("[HS]getUserInfoByPortalId: User's Email: " . json_encode($response));
		return $response;
	}

	static function ownerInfo($owner_id, $portal_id)
	{
		$tokens = PortalTokens::where('portal_id', $portal_id)->first()->toArray();
		$hub_url = "https://api.hubapi.com/owners/v2/owners/$owner_id";

		$res = Func::request(
			'GET',
			$hub_url,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				]
			]
		);
		if (!isset($res->portalId)) {
			Log::error("[Hubspot:ownerInfo] payload: " . json_encode($res));
			return false;
		}
		return $res;
	}

	static function properties($portal_id = null)
	{
		$types = [
			'phone',
			'mobilephone',
			'mobile_phone_number',
			'firstname',
			'lastname'
		];

		if (!$portal_id)
			return $types;
		$savedProps = self::savedProps($portal_id);
		if (!$savedProps)
			return $types;

		return array_merge($types, $savedProps);
	}

	static function getPhoneProps($portal_id)
	{
		// default props ( only sends default props)
		$props = ['phone', 'mobilephone'];
		return $props;

		// future:- to use props saved in DB by user
		try {
			$properties = Properties::select('name')->where([
				['portal_id', $portal_id],
				['type', 'phone'],
			])->get();

			if (!$properties)
				return $props;

			$savedProps = $properties->pluck('name')->toArray();
			$allProps = array_merge($props, $savedProps);
			$allProps = array_unique($allProps);
			return $allProps;
		} catch (Exception $e) {
			Log::error("[Hubspot:getPhoneProps] Exception " . $e->getMessage());
			return $props;
		}
	}

	static function phoneProps($portal_id = null)
	{
		$types = ['phone', 'mobilephone', 'mobile_phone_number'];
		if (!$portal_id)
			return $types;

		$savedProps = self::savedProps($portal_id, 'phone');
		if (!$savedProps)
			return $types;

		return array_merge($types, $savedProps);
	}

	static function savedProps($portal_id, $type = null)
	{
		if (!$type) {
			$properties = Properties::select('name')->where('portal_id', $portal_id)->get();
		} else {
			$properties = Properties::select('name')->where([
				['portal_id', $portal_id],
				['type', $type],
			])->get();
		}
		$properties = $properties ? $properties->pluck('name')->toArray() : false;
		return $properties;
	}

	static function hsPhone($properties, $portal_id, $wantVersion = false)
	{
		$phone = false;
		$types = self::phoneProps($portal_id);

		// if want versions and versions exists
		if (
			$wantVersion
			&& isset($properties->phone)
			&& !$properties->phone->value
			&& isset($properties->phone->versions)
		) {
			$versions = $properties->phone->versions;
			$versionPhones = [];
			foreach ($versions as $version) {
				$value = $version->value;
				if ($value && !in_array($value, $versionPhones)) {
					$versionPhones[] = $value;
				}
			}
			return $versionPhones ? ['isVersion' => true, 'phones' => $versionPhones] : false;
		}

		foreach ($types as $type) {
			$phone = $properties->$type->value ?? false;
			if ($phone)
				break;
		}
		return $phone;
	}

	static function getPhone($id, $portal_id, $wantVersion = false)
	{
		$tokens = self::getTokens($portal_id);
		$properties = Func::makePr(self::properties($portal_id));
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/$id/profile?" . $properties;

		$contact = Func::request(
			'GET',
			$hub_url,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				]
			]
		);

		// Log::info("[Hubspot:getPhone] url: $hub_url, response: ".json_encode($contact));
		if (isset($contact->status) || !isset($contact->properties)) {
			Log::error("[Hubspot:getPhone] " . json_encode($contact));
			return false;
		}

		$phone = self::hsPhone($contact->properties, $portal_id, $wantVersion);
		if ($phone && !is_array($phone))
			$phone = Func::parsePhone($phone);
		return $phone;
	}

	static function getUser($id, $portal_id)
	{
		$tokens = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/$id/profile";

		$contacts = Func::request(
			'GET',
			$hub_url,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				]
			]
		);

		if (!isset($contacts->properties))
			return false;

		return self::contactReader($contacts);
	}

	static function contactReader($contacts)
	{
		$portal_key = 'portal-id';
		$portal_id = $contacts->$portal_key;
		$properties = $contacts->properties;
		$user = [];

		foreach ($properties as $key => $property) {
			$user[$key] = $property->value ?? '';
		}

		// make sure first name and last name exist or it will give undefined error
		$user['firstname'] = $user['firstname'] ?? "";
		$user['lastname'] = $user['lastname'] ?? "";

		$user['fullname'] = $user['lastname']
			? $user['firstname'] . ' ' . $user['lastname']
			: $user['firstname'];
		if (isset($user['phone']) && $user['phone'])
			return $user;

		// find phone number on different properties
		$phoneProps = self::getPhoneProps($portal_id);
		foreach ($phoneProps as $prop) {
			$value = $properties->$prop->value ?? "";
			if (!$value)
				continue;

			$user['phone'] = $value;
			break;
		}
		return $user;
	}

	static function searchUser($phone, $portal_id)
	{
		$tokens = self::getTokens($portal_id);
		$response = Func::request(
			"GET",
			"https://api.hubapi.com/contacts/v1/search/query?q=" . $phone,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				]
			]
		);

		if (isset($response->contacts)) {
			$contact = $response->contacts[0];
		}
		if (!isset($contact) || !isset($contact->properties))
			return false;

		return self::contactReader($contact);
	}

	static function getEmailFromPhone($phone, $portal_id)
	{
		$users = [];
		$tokens = self::getTokens($portal_id);
		$contact = Func::request(
			"GET",
			"https://api.hubapi.com/contacts/v1/search/query?q=" . $phone,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				]
			]
		);
		if (isset($contact->contacts)) {
			foreach ($contact->contacts as $contact) {
				$firstname = $contact->properties->firstname->value ?? '';
				$lastname = $contact->properties->lastname->value ?? '';
				$fullname = $firstname;
				if ($lastname)
					$fullname .= ' ' . $lastname;
				$users[] = [
					'name' => $firstname,
					'fullname' => $fullname,
					'email' => $contact->properties->email->value ?? '',
					'owner_id' => $contact->properties->hubspot_owner_id->value ?? '',
					'objectId' => $contact->vid
				];
			}
		}
		return $users;
	}

	static function searchV3($portal_id, $q, $filter = false)
	{
		$users = [];
		$tokens = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/crm/v3/objects/contacts/search";

		$res = Func::request('POST', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'json' => $q
		]);

		if (isset($res->status)) {
			Log::error("[HubspotApp:searchV3] Error for $portal_id:" . json_encode($res));
			return false;
		}

		$results = $res->results ?? [];
		if (!$filter || !$results)
			return $results;

		foreach ($results as $contact) {
			$firstname = $contact->properties->firstname ?? "";
			$lastname = $contact->properties->lastname ?? "";
			$fullname = $lastname ? $firstname . ' ' . $lastname : $firstname;

			$users[] = [
				'name' => $firstname,
				'fullname' => $fullname,
				'email' => $contact->properties->email ?? '',
				'owner_id' => $contact->properties->hubspot_owner_id ?? '',
				'objectId' => $contact->id
			];
		}
		return $users;
	}

	static function getPortalId($access_token)
	{
		return Func::request(
			'GET',
			'https://api.hubapi.com/integrations/v1/me',
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $access_token"
				]
			]
		);
	}

	static function fetchList($list_id, $portal_id, $offset = null)
	{
		$tokens = self::getTokens($portal_id);
		$parts = ['count' => 100];
		if ($offset)
			$parts['vidOffset'] = $offset;
		$query = http_build_query($parts);

		$properties = Func::makePr(self::properties($portal_id));
		$hub_url = 'https://api.hubapi.com/contacts/v1/lists/' . $list_id . '/contacts/all?' . $properties . "&$query";

		$res = Func::request('GET', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			]
		]);

		if (!$res->contacts) {
			Log::error("FetchList: " . json_encode($res));
			return false;
		}
		return $res;
	}

	static function hsProperties($portal_id)
	{
		$tokens = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/properties/v1/contacts/groups/named/contactinformation?includeProperties=true";
		$properties = Func::request('GET', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			]
		]);
		return $properties->properties ?? false;
	}

	static function getEvent($id, $portal_id, $config)
	{
		$tokens = self::getTokens($portal_id);
		$hub_url = $config['url'] . "timeline/event/" . $config['hubspot_event'] . "/" . $id;

		return Func::request('GET', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			]
		]);
	}

	static function buildContactProps($data)
	{
		$props['properties'] = [];
		foreach ($data as $key => $value) {
			if (!empty($value)) {
				$props['properties'][] = [
					'property' => $key,
					'value' => $value
				];
			}
		}
		return $props;
	}

	public static function buildContactPropsFromMapping($mapping, $source,$staticProps = [])
	{
		$props['properties'] = [];

		foreach ($mapping as $hubspotField => $sourceField) {
			if (isset($source->$sourceField) && $source->$sourceField !== '') {
				$props['properties'][] = [
					'property' => $hubspotField,
					'value' => $source->$sourceField
				];
			}
		}

		foreach ($staticProps as $hubspotField => $value) {
			if (!empty($hubspotField) && $value !== '') {
				$props['properties'][] = [
					'name' => $hubspotField,
					'value' => $value
				];
			}
		}

		return $props;
	}


	static function addContactToList($portal_id, $list_id, $contacts)
	{
		$tokens = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/contacts/v1/lists/$list_id/add";

		$response = Func::request('POST', $hub_url, [
			'json' => $contacts,
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			]
		]);

		if (!isset($response->updated)) {
			Log::error("[HS]Error in addContactToList: " . json_encode($response));
			return false;
		}
		return true;
	}

	static function addAsLead($portalId, $input)
	{
		$tokens = self::getTokens('2720327');
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/createOrUpdate/email/$input[email]";
		$nameParts = explode(' ', $input['name']);
		$props = [
			'firstname' => ucfirst($nameParts[0]) ?? '',
			'lastname' => ucfirst($nameParts[count($nameParts) - 1]) ?? '',
			'email' => $input['email'],
			'phone' => '+' . $input['phone'],
			'additional_tag' => 'HubChat',
			'hubchat_portal_id_1' => $portalId,
			'hubchat_phone_number' => '+' . $input['phone'],
			'extension_downloaded' => true
		];

		// 1. add this user as a lead in hubspot
		// 2. update interest property to hubchat
		$properties = self::buildContactProps($props);
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'body' => $body
		];

		$response = Func::request('POST', $hub_url, $data);

		if (!isset($response->vid)) {
			Log::error("[HS]Error in addAsLead: " . json_encode($response));
			return false;
		}
		return $response->vid;
	}

	static function subCancelledUpdate($props, $email)
	{
		$tokens = self::getTokens('2720327');
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/createOrUpdate/email/$email";
		$properties = self::buildContactProps($props);
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'body' => $body
		];

		$response = Func::request('POST', $hub_url, $data);

		if (!isset($response->vid)) {
			Log::error("[HS]Error in subCancelledUpdate: " . json_encode($response));
			return false;
		}
		return $response->vid;
	}

	static function updateTimeline($message, $options)
	{

		$hsConfig = app()->on->hsConfig();
		$tokens = self::getTokens($options['portal_id']);
		$dialog_id = $options['portal_id'] . '.' . $options['instance_id'];

		if ($options['type'] == 'messages') {
			$phone = $message['fromMe']
				? $phone = explode("@", $message['chatId'])[0]
				: $phone = explode("@", $message['author'])[0];

			$blacklisted = false;
			$blacklist = \App\Models\Blacklist::where('portal_id', $options['portal_id'])->get()->pluck('phone');
			$blacklist = $blacklist ? $blacklist->toArray() : false;
			if ($blacklist && in_array($phone, $blacklist)) {
				Log::info("[Hubspot:updateTimeline] Number is blacklisted");
				return [];
			}
		}

		//Q:: No point processing messages which are coming from blacklisted user. Shouldn't we check that first and DB will update thru cron.
		//A:: Done ^, thanks for pointing out.
		if ($options['type'] == 'messages') {
			$dbName = '';
			$caSenderName = $message['fromMe'] ? $message['chatName'] : $message['senderName'];
			$caSenderName = (strpos($caSenderName, '+') !== false) ? '' : $caSenderName;
			if ($message['fromMe']) {
				// find this message in messages table and fetch the name of sender
				$sentMessage = \Illuminate\Support\Facades\DB::table('messages')
					->where('messages.id', $message['id'])
					->rightJoin('users', 'messages.uid', '=', 'users.id')
					->first();
				$sentMessage && $sentMessage->name && $dbName = $sentMessage->name;
			}
			//Q:: No point in replyJob for blocked contact.
			// make data and fire fcm for this [live chat]
			$liveUpdateMessage = [
				'id' => $message['id'],
				'chatId' => $message['chatId'],
				'sender' => $dbName,
				'name' => $caSenderName,
				'phone' => '+' . $phone,
				'time' => $message['time'],
				'fromMe' => $message['fromMe'],
				'senderName' => $message['senderName'] ?? '',
				'quotedMsgId' => $message['quotedMsgId'] ?? '',
				'quotedMsgBody' => $message['quotedMsgBody'] ?? '',
			];
			// handle quotedMsg
			if ($message['quotedMsgId']) {
				try {
					$quotedMsg = \App\Models\Messages::find($message['quotedMsgId']);
					if ($quotedMsg) {
						$quotedMsgBody = Func::makeMessageBody($quotedMsg, true);
						$liveUpdateMessage['body'] = Func::makeMessageBody((object) $message, false, $quotedMsgBody);
						$liveUpdateMessage['quotedMsgTime'] = $quotedMsg->time;
						$liveUpdateMessage['quotedNumber'] = $quotedMsg->messageNumber;
					} else {
						$liveUpdateMessage['body'] = Func::makeMessageBody((object) $message);
					}
				} catch (Exception $e) {
					// unset the msg id when it's not found because can't find the message
					$liveUpdateMessage['body'] = Func::makeMessageBody((object) $message);
				}
			} else {
				$liveUpdateMessage['body'] = Func::makeMessageBody((object) $message);
			}
			dispatch(new \App\Jobs\PusherMessageJob($dialog_id, $liveUpdateMessage));
			if ($options['portal_id'] == 5255280) {
				dispatch(new \App\Jobs\WebhookJob($dialog_id, $liveUpdateMessage));
			}

			// save this message into DB
			try {
				$message['dialogId'] = $dialog_id;
				(!isset($message['isForwarded']) || !$message['isForwarded']) && $message['isForwarded'] = 0;
				Messages::updateOrCreate(['id' => $message['id']], $message);
			} catch (Exception $e) {
				Log::error("[Hubspot:updateTimeline] Exception when saving message to DB: "
					. $e->getMessage());
			}
		}

		if ($options['type'] == 'ack') {
			// update message status
			try {
				Messages::where(['id' => $message['id']])->update([
					'status' => $message['status'],
					'statusAt' => time(),
				]);
			} catch (Exception $e) {
				Log::info("[Hubspot:updateTimeline] Exception when updating message ACK status: "
					. $e->getMessage());
			}
			$phone = explode("@", $message['chatId'])[0];
			if ($message['status'] == 'sent')
				return ['message' => 'sent status already updated in timeline'];
		}

		// $phoneProps = self::getPhoneProps($options['portal_id']);
		// $nationalNumber = Func::getNationalNumber($phone);
		// $nationalNumber = $nationalNumber ? $nationalNumber : $phone;
		$q = [];
		$phoneProps = ['phone', 'mobilephone'];
		$internationNumber = Func::getInternationNumber($phone);
		foreach ($phoneProps as $prop) {
			$phoneValue = $phone;
			// if($prop == 'hs_searchable_calculated_phone_number') {
			// 	$phoneValue = $nationalNumber;
			// }
			$q['filterGroups'][] = [
				'filters' => [
					[
						'propertyName' => $prop,
						'operator' => 'CONTAINS_TOKEN',
						'value' => $phoneValue
					]
				]
			];
		}
		// if international number found
		$internationNumber && $q['filterGroups'][] = [
			'filters' => [
				[
					'propertyName' => 'phone',
					'operator' => 'CONTAINS_TOKEN',
					'value' => $internationNumber
				]
			]
		];
		$q['properties'] = ['firstname', 'lastname', 'email', 'hubspot_owner_id'];
		$users = self::searchV3($options['portal_id'], $q, true);
		if ($users === false) {
			return ['status' => 'error', 'message' => 'unable to search user, check logs'];
		}

		// Search V1
		// $users = self::getEmailFromPhone($phone, $options['portal_id']);
		if (!$users) {
			$settings = \App\Models\Settings::where([
				['portal_id', '=', $options['portal_id']],
				['is_owner', '=', 1]
			])->first();
			if ($options['instance_id'] == 94) {
				// Log::info("Create contact is disabled for instance_id".$options['instance_id']);
			} else if (isset($settings) && !$settings->create_user) {
				// Log::info("Create contact is disabled for ".$options['portal_id']);
				// return response("Create contact is disabled for ".$options['portal_id']);
			} else {
				// Create new user if msg is an incoming msg
				if (($options['type'] != 'ack') && !$message['fromMe']) {
					$shouldCreateContact = true;

					Log::info("[Hubspot:updateTimeline] New contact creating for portal_id:" . $options['portal_id'] . ", phone:" . $phone);

					$getDialog = \App\Models\Dialogs::where([
						'dialog_id' => $dialog_id,
						'id' => $message['chatId']
					])->first();

					// don't create contact if the dialog is saved within last 10 minutes
					if ($getDialog) {
						Log::info("[Hubspot:updateTimeline] dialoge found: " . json_encode($getDialog));

						$time = strtotime($getDialog->created_at);
						if ((time() - $time) < 600) {
							$shouldCreateContact = false;

							Log::info("[Hubspot:updateTimeline] time is less than 10 minutes So closing");
							//skipping contact creating
							if (!$getDialog->object_id)
								return;

							// but updating the timeline and sending notification
							$users[] = ['objectId' => $getDialog->object_id];
						}
					}

					if ($shouldCreateContact) {
						Log::info("[Hubspot:updateTimeline] Creating contact for portal_id:" . $options['portal_id'] . ", phone:" . $phone);
						// saving dialog and creating contact
						$dialog = [
							'id' => $message['chatId'],
							'dialog_id' => $dialog_id,
							'phone' => '+' . $phone,
							'name' => $caSenderName,
							'created' => true,
							'time' => $message['time'],
							'created_at' => date('Y-m-d H:i:s')
						];
						Func::updateDialogs($dialog);

						$props = self::buildContactProps([
							'firstname' => $message['senderName'],
							'phone' => '+' . $phone,
						]);

						$userResponse = self::createContact($options['portal_id'], $props);
						if (!$userResponse)
							return;

						$users[] = ['objectId' => $userResponse->vid];
					}
				}
			}
		}

		if ($options['type'] == 'ack') {
			//Check if multiple users or single user
			// Log::info("User: ".json_encode($users));
			$data = [];
			$eventId = $message['id'];
			$eventUrl = "timeline/event";

			$dbMsg = Messages::where('id', $eventId)->first();
			if (!$dbMsg)
				return ['message' => 'message not found'];

			$caSenderName = (strpos($dbMsg->senderName, '+') !== false) ? '' : $dbMsg->senderName;
			$msgBody = Func::getMsgBody($dbMsg);
			if (count($users) === 1) {
				$data = [
					"id" => $eventId,
					"objectId" => $users[0]['objectId'],
					"eventTypeId" => $hsConfig['hubspot_event'],
					"status" => $dbMsg->status,
					"message" => $msgBody,
					"phone" => explode('@', $dbMsg->chatId)[0]
				];
				$caSenderName && $data['status'] = $dbMsg->status . ', sender: ' . $caSenderName;
			} else {
				$data['eventWrappers'] = [];
				$eventUrl = "timeline/event/batch";
				foreach ($users as $user) {
					$event = [
						"id" => $dbMsg->id . '.' . $user['objectId'],
						"objectId" => $user['objectId'],
						"eventTypeId" => $hsConfig['hubspot_event'],
						"status" => $dbMsg->status,
						"message" => $msgBody,
						"phone" => explode('@', $dbMsg->chatId)[0]
					];
					$caSenderName && $event['status'] = $dbMsg->status . ', sender: ' . $caSenderName;
					$data['eventWrappers'][] = $event;
				}
			}
			$res = Func::request(
				'PUT',
				$hsConfig['url'] . $eventUrl,
				[
					'headers' => [
						'Content-Type' => 'application/json',
						"Authorization" => "Bearer $tokens[access_token]"
					],
					'json' => $data
				]
			);
			return $res ? $res : $users;
		}

		// send notification
		// check blacklist while sending notifications. Also, no point processing msgBody if message is not fromMe. So get the MessageBody after checking.
		$msgBody = Func::getMsgBody((object) $message);
		if (!$message['fromMe']) {
			$senderName = $message['senderName'] ?: $phone;
			$notification = [
				'notification' => [
					'title' => 'New message from ' . $senderName,
					'body' => $msgBody,
					'icon' => 'https://cdn2.hubspot.net/hubfs/2720327/hubchat-logo.png',
				]
			];
			if ($users) {
				$vid = $users[0]['objectId'];
				$notification['fcm_options'] = [
					'analytics_label' => 'hc-notification',
					'link' => 'https://app.hubspot.com/contacts/' . $options['portal_id'] . '/contact/' . $vid
				];

				$notification['users'] = [];
				foreach ($users as $user) {
					if (!isset($user['owner_id']))
						continue;

					$ownerId = $user['owner_id'];
					if (!$ownerId)
						continue;

					if (!isset($notification['users'][$ownerId])) {
						$notification['users'][$ownerId] = $user;
					}
				}
			}
			dispatch(new \App\Jobs\NotificationsJob($options, $notification, $message['id']));
		}

		// creating timeline events
		if (!$users)
			return;

		// update name for HubSpot contacts
		if (!$message['fromMe'] && $caSenderName) {
			Log::info("[HubSpot:updateTimeline] Update contact called dialog_id: " . $dialog_id);
			try {
				$dbDialog = \App\Models\Dialogs::where([
					'dialog_id' => $dialog_id,
					'id' => $message['chatId']
				])->first();
				if ($dbDialog && !$dbDialog->name && $dbDialog->object_id) {
					$props = self::buildContactProps([
						'firstname' => explode(' ', $caSenderName)[0] ?? '',
						'lastname' => explode(' ', $caSenderName)[1] ?? '',
						'phone' => '+' . $phone,
					]);
					$isUpdated = self::updateContact($options['portal_id'], $props, $dbDialog->object_id);
					Log::info("[HubSpot:updateTimeline] update response from Hubspot " . $isUpdated);
				}
			} catch (Exception $e) {
				Log::error("[Hubspot:updateTimeline] Exception when saving name to hubSpot " . $e->getMessage());
			}
		}

		// save this user as dialog
		$dialogName = $users[0]['fullname'] ?? $caSenderName;
		$dialog = [
			'id' => $message['chatId'],
			'dialog_id' => $dialog_id,
			'object_id' => $users[0]['objectId'],
			'phone' => '+' . $phone,
			'name' => $dialogName,
			'time' => $message['time']
		];
		Func::updateDialogs($dialog);

		//seperate single and multiple users
		$count = 0;
		$data['eventWrappers'] = [];
		// Log::info("User for events: ".json_encode($users));
		if (count($users) === 1) {
			// Log::info("User: ".json_encode($users));
			$eventUrl = "timeline/event";
			if ($message['fromMe']) {
				$data = [
					"id" => $message['id'],
					"eventTypeId" => $hsConfig['hubspot_event'],
					"status" => "sent, sender: " . $message['senderName'],
					"message" => $msgBody,
					"phone" => $options['account_phone'],
					"objectId" => $users[0]['objectId']
				];
			} else {
				$data = [
					"id" => $message['id'],
					"eventTypeId" => $hsConfig['user_event'],
					"user" => $dialogName,
					"phone" => $options['account_phone'],
					"message" => $msgBody,
					"objectId" => $users[0]['objectId']
				];
			}
		} else {
			$eventUrl = "timeline/event/batch";
			foreach ($users as $user) {
				if ($message['fromMe']) {
					$event = [
						"id" => $message['id'] . '.' . $user['objectId'],
						"eventTypeId" => $hsConfig['hubspot_event'],
						"status" => "sent, sender: " . $message['senderName'],
						"phone" => $options['account_phone'],
						"message" => $msgBody,
						"objectId" => $user['objectId']
					];
				} else {
					$event = [
						"id" => $message['id'] . '.' . $user['objectId'],
						"eventTypeId" => $hsConfig['user_event'],
						"user" => $dialogName,
						"phone" => $options['account_phone'],
						"message" => $msgBody,
						"objectId" => $user['objectId']
					];
				}
				$data['eventWrappers'][] = $event;
				$count++;
			}
		}

		$res = Func::request(
			'PUT',
			$hsConfig['url'] . $eventUrl,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				],
				'json' => $data
			]
		);
		return $res ? $res : $users;
	}

	static function ticketAssociations($portalId, $ticketId)
	{
		$tokens = self::getTokens($portalId);
		$api_url = "https://api.hubapi.com/crm/v3/objects/tickets/$ticketId/associations/contact?paginateAssociations=false&limit=500";

		$res = Func::request(
			'GET',
			$api_url,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $tokens[access_token]"
				]
			]
		);

		if (!isset($res->results)) {
			Log::error("[Hubspot:ticketAssociations] Error " . json_encode($res));
			return false;
		}
		return $res->results;
	}

	static function fetchBatchContacts($portalId, $ids)
	{
		$tokens = self::getTokens($portalId);
		$api_url = "https://api.hubapi.com/crm/v3/objects/contacts/batch/read";

		$res = Func::request('POST', $api_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'json' => ['inputs' => $ids, 'properties' => ['phone', 'email', 'firstname']]
		]);

		if (!isset($res->results)) {
			Log::error("[Hubspot:fetchBatchContacts] Error " . json_encode($res));
			return false;
		}
		return $res->results;
	}

	public static function assignCustomer($user, $hsPortalId)
	{
		$props['properties'][] = ['property' => 'hubchat_customer', 'value' => true];
		$props['properties'][] = ['property' => 'hubchat_portal_id_1', 'value' => $hsPortalId];
		$nameParts = explode(' ', $user['name']);
		if (isset($nameParts[0]))
			$user['firstname'] = $nameParts[0];
		if (isset($nameParts[1]))
			$user['lastname'] = $nameParts[1];
		$keys = [
			'email' => 'email',
			'firstname' => 'firstname',
			'lastname' => 'lastname',
			'hubchat_website' => 'domain',
			'hubchat_phone_number' => 'phone',
		];

		foreach ($keys as $key => $name) {
			if (!isset($user[$name]) || !$user[$name])
				continue;
			$props['properties'][] = [
				'property' => $key,
				'value' => $user[$name]
			];
		}

		return self::createOrUpdateContact(2720327, $props, $user['email']);
	}

	public static function tokenStatus($refreshToken)
	{
		$hub_url = "https://api.hubapi.com/oauth/v1/refresh-tokens/$refreshToken";
		$res = Func::request('GET', $hub_url);
		if (!$res)
			return false;

		$status = $res->status ?? '';
		return $status ? $status : 'ok';
	}

	static function buildCompanyProps($data)
	{
		$props['properties'] = [];
		foreach ($data as $key => $value) {
			if (isset($value)) {
				$props['properties'][] = [
					'name' => $key,
					'value' => $value
				];
			}
		}
		return $props;
	}

	public static function buildCompanyPropsFromMapping($mapping, $source,$staticProps = [])
	{
		$props['properties'] = [];

		foreach ($mapping as $hubspotField => $sourceField) {
			if (isset($source->$sourceField) && $source->$sourceField !== '') {
				$props['properties'][] = [
					'name' => $hubspotField,
					'value' => $source->$sourceField
				];
			}
		}

		foreach ($staticProps as $hubspotField => $value) {
			if (!empty($hubspotField) && $value !== '') {
				$props['properties'][] = [
					'name' => $hubspotField,
					'value' => $value
				];
			}
		}

		return $props;
	}


	public static function getCompanyFields($portal_id)
	{
		$tokens = self::getTokens($portal_id);
		// $token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/properties/v2/companies/properties";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			]
		];

		$response = Func::request('GET', $hub_url, $data);
		if (!isset($response)) {
			Log::error("[HS]Error in getCompany properties: " . json_encode($response));
			return false;
		}
		Log::info("[getCompanyProperties] " . json_encode($response));
		return $response;

	}

	static function getCompany($portal_id, $companyId)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/companies/v2/companies/$companyId";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];

		$response = Func::request('GET', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in getCompany: " . json_encode($response));
			return false;
		}
		Log::info("[InboundgetCompany] for $portal_id\n" . $companyId);
		return $response;
	}

	static function createCompany($portal_id, $properties)
	{
		$token = self::getTokens($portal_id);
		// $token = env('HUBSPOT_KEY');
		try {
			$hub_url = "https://api.hubapi.com/companies/v2/companies";
			$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

			$data = [
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $token[access_token]"
				],
				'body' => $body
			];

			$response = Func::request('POST', $hub_url, $data);
			if (!isset($response->companyId)) {
				Log::error("[HS] Error in createCompany: " . json_encode($response));
				return false;
			}
			Log::info("[InboundCompanyCreate] for $portal_id\n" . $body);
			return $response;
		} catch (\Throwable $th) {
			Log::error("[HS] Error in createCompany: " . $th->getMessage());
		}
	}

	static function updateCompany(
		$portal_id,
		$companyId,
		$properties
	) {
		$token = self::getTokens($portal_id);
		// $token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/companies/v2/companies/$companyId";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token[access_token]"
			],
			'body' => $body
		];

		$response = Func::request('PUT', $hub_url, $data);
		if (!isset($response->companyId)) {
			Log::error("[HS]Error in UpdateCompany: " . json_encode($response));
			Log::error("[HS]Error in UpdateCompany body: " . $body);
			return false;
		}
		Log::info("[InboundUpdateCompany] $companyId for $portal_id successfull\n");
		return $response;
	}

	static function connectCompanyToDeal($portal_id, $dealId, $companyId)
	{
		// $tokens = self::getTokens($portal_id);
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/deals/v1/deal/$dealId/associations/COMPANY?id=$companyId";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];

		$response = Func::request('PUT', $hub_url, $data);
		if (isset($response->status)) {
			Log::error("[HS]Error in connectCompanyToDeal: " . json_encode($response));
			return false;
		}
		Log::info("[InboundconnectCompanyToDeal] for $dealId\n" . $companyId);
		return $response;
	}
	static function connectContactToDeal($portal_id, $dealId, $contactId)
	{
		// $tokens = self::getTokens($portal_id);
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/deals/v1/deal/$dealId/associations/CONTACT?id=$contactId";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];

		$response = Func::request('PUT', $hub_url, $data);
		if (isset($response->status)) {
			Log::error("[HS]Error in connectContactToDeal: " . json_encode($response));
			return false;
		}
		Log::info("[InboundconnectContactToDeal] for $dealId\n" . $contactId);
		return $response;
	}

	static function searchContact($email)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/contacts/v1/search/query?q=$email";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];

		$response = Func::request('GET', $hub_url, $data);
		if (isset($response->status)) {
			Log::error("[HS]Error in searchContact: " . json_encode($response));
			return false;
		}
		Log::info("[searchContact] for $email");
		return $response;
	}

	static function createOrUpdateContact($portal_id,$properties, $email)
	{
		// $token = env('HUBSPOT_KEY');
		if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
			Log::error("[HS] Invalid email passed to createOrUpdateContact: $email");
			return false;
		}

		Log::info("[HS] valid email passed to createOrUpdateContact: $email");
		Log::info("[HS] properties passed to createOrUpdateContact: " . json_encode($properties));

		$token = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/createOrUpdate/email/$email";

		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token[access_token]"
			],
			'body' => $body
		];

		$response = Func::request('POST', $hub_url, $data);

		if (!isset($response->vid)) {
			Log::error("[HS]Error in createOrUpdateContact: " . json_encode($response));
			return false;
		}
		Log::info("[createOrUpdateContact] for $response->vid");
		return $response->vid;
	}

	static function getContact($portal_id, $email)
	{
		// $tokens = self::getTokens($portal_id);
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/email/$email/profile";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];

		$response = Func::request('GET', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in getContact: " . json_encode($response));
			return false;
		}
		Log::info("[InboundgetContact] for $portal_id\n" . $email);
		return $response;
	}

	static function getContactById($contactId)
	{
		// $tokens = self::getTokens($portal_id);
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/$contactId/profile";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];

		$response = Func::request('GET', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in getContactById: " . json_encode($response));
			return false;
		}
		Log::info("[InboundgetContactById] for $contactId\n" . $contactId);
		return $response;
	}

	static function createContact($properties)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/contacts/v1/contact";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			],
			'body' => $body
		];

		$response = Func::request('POST', $hub_url, $data);
		if (!isset($response->vid)) {
			Log::error("[HS]Error in createContact: " . json_encode($response));
			return false;
		}
		Log::info("[InboundcreateContact] for $response->vid");
		return $response;
	}

	static function updateContact($portal_id, $vid, $properties)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/$vid/profile";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			],
			'body' => $body
		];

		$response = Func::request('POST', $hub_url, $data);

		if (isset($response->status)) {
			Log::error("[HS]Error in updateContact: " . json_encode($response));
			return false;
		}
		Log::info("[InboundupdateContact] $vid for $portal_id successfull\n");
		return true;
	}

	static function getDeal($portal_id, $dealId)
	{
		$token = self::getTokens($portal_id);
		// $token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/deals/v1/deal/$dealId";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token[access_token]"
			]
		];

		$response = Func::request('GET', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in getDeal: " . json_encode($response));
			return false;
		}
		Log::info("[InboundgetDeal] for $portal_id\n" . $dealId);
		return $response;
	}

	public static function getDealFields($portal_id)
	{
		$token = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/properties/v2/deals/properties";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token[access_token]"
			]
		];

		$response = Func::request('GET', $hub_url, $data);

		if (!isset($response)) {
			Log::error("[HS]Error in getDeal properties: " . json_encode($response));
			return false;
		}

		Log::info("[getDealProperties] " . json_encode($response));
		return $response;
	}



	/**
	 * getAllDeals
	 *
	 * @param  mixed $portal_id
	 * @return void
	 */
	public static function getAllDeals()
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/crm/v3/objects/deals?limit=100&properties=equipment_id&properties=hs_object_id&archived=false" .
			"&properties=parent_name" .
			"&properties=hubspot_id" .
			"&properties=description" .
			"&properties=start_date___date_picker" .
			"&properties=start_hours___numeric" .
			"&properties=lastsmrdate___date_picker" .
			"&properties=lastsmr_date___numeric" .
			"&properties=latitude" .
			"&properties=longitude" .
			"&properties=hours_per_day" .
			"&properties=hours_per_day_range" .
			"&properties=inventory" .
			"&properties=fleet" .
			"&properties=fleet_type" .
			"&properties=status" .
			"&properties=new_used" .
			"&properties=cbgrp" .
			"&properties=hours_per_day_override" .
			"&properties=uc_target_wear_percent" .
			"&properties=exclude_uc_tracking" .
			"&properties=complete" .
			"&properties=satellite_identifier" .
			"&properties=parent_id" .
			"&properties=parent_type" .
			"&properties=cbmod" .
			"&properties=cbmyr" .
			"&properties=cbmak" .
			"&properties=serial_no_" .
			"&properties=cbord" .
			"&properties=equipment_number" .
			"&properties=category_id" .
			"&properties=stdwtdenddate" .
			"&properties=extwarrantyenddate" .
			"&properties=warranty_type___multi_select" .
			"&properties=basicwarrantyhrs" .
			"&properties=extwarrantyhrs" .
			"&properties=lease_expiry_date___date_picker" .
			"&properties=tier" .
			"&properties=dealname";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			]
		];
		$allDeals = [];

		try {
			do {
				$response = Func::request('GET', $hub_url, $data);
				if (!isset($response->results)) {
					Log::error("[HS]Error in getDeal: " . json_encode($response));
					return false;
				}

				// Process the deals from the current page
				foreach ($response->results as $deal) {
					$allDeals[] = $deal->properties;
				}

				if (isset($response->paging->next->link)) {
					$nextPageUrl = $response->paging->next->link;
					$hub_url = $nextPageUrl;
				} else {
					break; // No more pages
				}
			} while (true);

			return $allDeals;
		} catch (\Throwable $th) {
			dd($th->getMessage());
		}
	}

	/**
	 * deleteDeal
	 *
	 * @param  mixed $dealId
	 * @return void
	 */
	public static function deleteDeal($dealId)
	{
		try {
			$token = env('HUBSPOT_KEY');
			echo "[HS] deleting deal with ID: " . $dealId . PHP_EOL;
			$deleteUrl = 'https://api.hubapi.com/crm/v3/objects/deals/' . $dealId;

			$data = [
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $token"
				]
			];
			$response = Func::request('DELETE', $deleteUrl, $data);

			echo "$response [HS] deleted deal with ID: " . $dealId . PHP_EOL;
			return true;
		} catch (\Throwable $th) {
			echo $th->getMessage();
		}
	}

	static function createDeal($portal_id, $properties)
	{
		$token = self::getTokens($portal_id);
		// $token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/deals/v1/deal";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token[access_token]"
			],
			'body' => $body
		];

		$response = Func::request('POST', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in createDeal: " . json_encode($response));
			return false;
		}
		Log::info("[HS] createDeal successfull for : " . $body);
		return $response;
	}

	static function updateDeal($portal_id, $dealId, $properties)
	{
		$token = self::getTokens($portal_id);
		// $token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/deals/v1/deal/$dealId";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token[access_token]"
			],
			'body' => $body
		];

		$response = Func::request('PUT', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in updateDeal: " . json_encode($response));
			return false;
		}
		Log::info("[HS] updateDeal for $dealId successfull : " . $body);
		return $response;
	}

	static function buildDealProps($data)
	{
		$props['properties'] = [];
		foreach ($data as $key => $value) {
			$props['properties'][] = [
				'name' => $key,
				'value' => $value
			];
		}
		return $props;
	}

	public static function buildDealPropsFromMapping($mapping, $source,$staticProps = [])
	{
		$props = ['properties' => []];

		foreach ($mapping as $hubspotField => $sourceField) {
			if (!empty($hubspotField) && isset($source->$sourceField) && $source->$sourceField !== '') {
				$props['properties'][] = [
					'name' => $hubspotField,
					'value' => $source->$sourceField
				];
			}
		}

		foreach ($staticProps as $hubspotField => $value) {
			if (!empty($hubspotField) && $value !== '') {
				$props['properties'][] = [
					'name' => $hubspotField,
					'value' => $value
				];
			}
		}

		return $props;

	}

	static function addContactToCompany($companyId, $contactId)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/companies/v2/companies/$companyId/contacts/$contactId";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			],
		];

		$response = Func::request('PUT', $hub_url, $data);
		if (!isset($response->properties)) {
			Log::error("[HS]Error in addContactToCompany: " . json_encode($response));
			return false;
		}
		Log::info("[addContactToCompany] for $companyId\n" . $contactId);
		return $response;
	}

	public static function getContactFields($portal_id)
	{
		$tokens = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/properties/v2/contacts/properties";

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			]
		];

		$response = Func::request('GET', $hub_url, $data);

		if (!isset($response)) {
			Log::error("[HS]Error in getContact properties: " . json_encode($response));
			return false;
		}

		Log::info("[getContactProperties] " . json_encode($response));
		return $response;
	}


	static function deleteCompany($companyId)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/companies/v2/companies/$companyId";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			],
		];
		$response = Func::request('DELETE', $hub_url, $data);
		return $response;
	}

	static function fetchOwners()
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/owners/v2/owners";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			],
		];
		$response = Func::request('GET', $hub_url, $data);
		return $response;
	}

	/**
	 * getAssociations
	 *
	 * @param  mixed $fromId
	 * @param  mixed $associatedFrom
	 * @param  mixed $associatedTo
	 * @return void
	 */
	public static function getAssociations($fromId, $associatedFrom, $associatedTo)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/crm/v4/objects/{$associatedFrom}/{$fromId}/associations/{$associatedTo}?limit=500";
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $token"
			],
		];

		$response = Func::request('GET', $hub_url, $data);

		if (!isset($response->results)) {
			Log::error("[HS]Error in getDealAssociations: " . json_encode($response));
			return false;
		}

		$associatedIds = array_column($response->results, 'toObjectId');

		$formattedIds = array_map(function ($id) use ($fromId) {
			return [
				'from' => ['id' => $fromId],
				'to' => [['id' => $id]]
			];
		}, $associatedIds);

		return [$associatedIds, $formattedIds];
	}

	/**
	 * deleteDealAssociations
	 *
	 * @param  mixed $format
	 * @param  mixed $associatedWith
	 * @return void
	 */
	public static function deleteAssociations($format, $associatedFrom, $associatedTo)
	{
		$token = env('HUBSPOT_KEY');
		$hub_url = "https://api.hubapi.com/crm/v4/associations/{$associatedFrom}/{$associatedTo}/batch/archive";

		$res = Func::request(
			'POST',
			$hub_url,
			[
				'headers' => [
					'Content-Type' => 'application/json',
					"Authorization" => "Bearer $token"
				],
				'json' => ['inputs' => $format]
			],
		);

		if (!$res) {
			Log::error("[HS]Error in getDealAssociations: " . json_encode($res));
			return false;
		}

		return true;
	}

	public static function addHubspotProperty($portal_id,$objectType, $propertyData)
	{
		$token = self::getTokens($portal_id);

		$url = "https://api.hubapi.com/properties/v1/{$objectType}/properties";

		$response = Http::withToken($token['access_token'])->post($url, $propertyData);

		Log::info("[HubspotApp:createPropertyfor] Raw response: " . $response->body());

		if ($response->failed()) {
			Log::error("[HubspotApp:createPropertyfor] Failed with status " . $response->status());
			return false;
		}


		Log::info("[HubspotApp:createPropertyfor] $objectType:$portal_id : ".json_encode($response->body()));

        return $response->body();
		
	}

	public static function deleteHubspotProperty($portal_id,$objectType, $propertyName)
	{
		$token = self::getTokens($portal_id);

		$url = "https://api.hubapi.com/properties/v1/{$objectType}/properties/named/{$propertyName}";

		$response = Http::withToken($token['access_token'])->delete($url)->object();
		
		Log::info("[HubspotApp:deletePropertyfor] $objectType:$portal_id : ".json_encode($response));
		return $response;
	}

	static function fetchUsers($portal_id)
	{
		$token = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/crm/v3/owners/";

		$response = Http::withToken($token['access_token'])->get($hub_url)->object();
		
		Log::info("[HubspotApp:FetchUsers] : $portal_id : ".json_encode($response));
		return $response;
	}

	public static function companyExists($hubspotCompanyId, $portalId)
	{
		$token = self::getTokens($portalId);
		$url = "https://api.hubapi.com/crm/v3/objects/companies/{$hubspotCompanyId}";

		$headers = [
			'headers' => [
				'Content-Type'  => 'application/json',
				'Authorization' => "Bearer {$token['access_token']}"
			]
		];

		$response = Func::request('GET', $url, $headers);

		// Check for standard HubSpot 404 object not found structure
		if (isset($response->status) && $response->status === 'error') {
			Log::warning("[HS]companyExists check failed: " . json_encode($response));
			return false;
		}

		// If it has an 'id', the company exists
		return isset($response->id);
	}

	public static function dealExists($portalId, $dealId)
	{
		$token = self::getTokens($portalId);
		$url = "https://api.hubapi.com/crm/v3/objects/deals/{$dealId}";

		$headers = [
			'headers' => [
				'Content-Type'  => 'application/json',
				'Authorization' => "Bearer {$token['access_token']}"
			]
		];

		$response = Func::request('GET', $url, $headers);

		if (isset($response->status) && $response->status === 'error') {
			Log::warning("[HS] Deal not found in dealExists(): " . json_encode($response));
			return false;
		}

		return isset($response->id);
	}

}
