<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use File;
use Storage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use PDO;
use App\Models\User;
use App\Models\PortalTokens;
use App\Helpers\Hubspot;
use App\Hubspot\Crm\CrmService;
use App\Helpers\Func;
use Illuminate\Support\Facades\Crypt;


class HubSpotController extends Controller
{
    public function authenticate(Request $request){
        $scopes = config('hsapp.current.scopes');
        $hsApp = config('hsapp.current.auth');
        $auth_url = "https://app.hubspot.com/oauth/authorize?client_id=$hsApp[client_id]&scope=$scopes&redirect_uri=$hsApp[redirect_uri]";

        return redirect($auth_url);
    }

    public function callback(Request $request){
        $input = $this->validate($request, [
			'code' => 'required'
		]);

        Log::info('HubSpot callback received with code: ' . $input['code']);
        $hsApp = config('hsapp.current.auth');
        Log::info('HubSpot auth config: ' . json_encode($hsApp));

		// fetch tokens
		$tokens = Hubspot::createTokens($input['code']);
        Log::info('HubSpot token response: ' . json_encode($tokens));

        if(!isset($tokens->access_token)) {
             Log::info('Authorization expired: Code is expired please try again');
             redirect('/error');
		}

        $portal = Hubspot::getPortalId($tokens->access_token);
        
		if(!isset($portal->portalId)) {
			Log::info('Authorization expired: There was an error find portal, please try again');
             redirect('/error');
		}

        $hubDomian = Hubspot::getHubDomain($tokens->access_token);
		if(!isset($hubDomian->hub_domain)) {
            Log::info('Authorization expired: There was an error find hubsot domain, please try again');
            redirect('/error');
		}

        //get user's detail by access token
		$user = Hubspot::getUserInfoByPortalId($tokens->access_token);
        if($user){
            try {
			User::updateOrCreate([
                    'portal_id' => $portal->portalId,
                    'user_id'   => $user->user_id,
                    'email'     => $user->user,
                ],
                [
                    'role'       => 'Admin',
                    'updated_at' => now(),
                ]);
            } catch(Exception $e) {
                Log::error("[HubSpotController:callback] user insert or update error: ".$e->getMessage());
            }
        }

		$saved = PortalTokens::updateOrInsert(
			['portal_id' => $portal->portalId],
			[
				'access_token' => $tokens->access_token,
				'refresh_token' => $tokens->refresh_token,
				'expires_in' => $tokens->expires_in,
				'domain' => $hubDomian->hub_domain
			]
		);

		if(!$saved){
			 Log::info('Faile to save: There was an error in save, please try again');
            redirect('/error');
		}
        $encryptedUserId = urlencode(Crypt::encryptString($user->user_id));

        return redirect("/wizard?portal_id={$portal->portalId}&email=" . urlencode($user->user) . "&user_id={$encryptedUserId}");


    }

    public function crm(Request $request, CrmService $crm){
        $input = $request->input();

        // Parse phone number
        $objectId = '964252'; //101734795
        $phone = $request->input('phone') ?: $request->input('mobilephone') ?: '';
        $phone = Func::parsePhone($phone);

        // Get response from the service
        $response = $crm->handleRequest($input, $phone, $objectId);

        return response()->json($response);
    }

}
