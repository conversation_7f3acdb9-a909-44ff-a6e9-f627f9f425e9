<template>
  <div class="flex flex-col min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="flex-shrink-0 p-6 bg-white border-b border-gray-200">
      <h2 class="text-2xl font-bold text-gray-900 mb-3">User Mapping</h2>
      <p class="text-sm text-gray-600 leading-relaxed">
        RMS app integration will be available only for mapped HubSpot-RMS user accounts.
        <a href="#" class="text-blue-600 hover:text-blue-800 underline">Learn more</a>
      </p>
    </div>

    <!-- Table Container with Sticky Header -->
    <div class="flex-1 flex flex-col overflow-hidden bg-white">
      <!-- Sticky Table Header -->
      <div class="sticky top-0 z-10 bg-gray-50 border-b border-gray-200">
        <div class="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-gray-900">
          <div class="col-span-4">HubSpot Member</div>
          <div class="col-span-4">Email ID</div>
          <div class="col-span-4">User Status</div>
        </div>
      </div>

      <!-- Scrollable Table Body -->
      <div class="flex-1 overflow-y-auto pb-4" style="max-height: calc(100vh - 220px);">

        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center py-16">
          <div class="text-center">
            <svg class="animate-spin mx-auto h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Loading...</h3>
            <p class="mt-1 text-sm text-gray-500">Fetching HubSpot Users...</p>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading && (!localUsers || localUsers.length === 0)" class="flex items-center justify-center py-16">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No fields to map</h3>
            <p class="mt-1 text-sm text-gray-500">No Users found from hubspot.</p>
          </div>
        </div>

        <!-- Table Rows -->
        <div v-for="(user, i) in sortedUsers" :key="user.email || i"
             class="grid grid-cols-12 gap-4 px-6 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150"
             :class="{ 'mb-4': i === sortedUsers.length - 1 }">
          <!-- HubSpot Member -->
          <div class="col-span-4 flex items-center">
            <div class="min-w-0 flex-1">
              <div class="text-sm font-medium text-gray-900 truncate">{{ user.name }}</div>
              <div class="text-xs text-gray-500 truncate">{{ user.firstName }} {{ user.lastName }}</div>
            </div>
          </div>

          <!-- Email ID -->
          <div class="col-span-4 flex items-center">
            <div class="min-w-0 flex-1">
              <div class="text-sm text-gray-900 truncate">{{ user.email }}</div>
            </div>
          </div>

          <!-- User Status Dropdown -->
          <div class="col-span-4 flex items-center">
            <select
              v-model="user.status"
              :disabled="originalAdminUsers.has(user.email)"
              class="w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-150"
              :class="{
                'bg-gray-100 cursor-not-allowed text-gray-600': originalAdminUsers.has(user.email),
                'bg-white': !originalAdminUsers.has(user.email)
              }">
              <option disabled value="">Select user type...</option>
              <option value="Admin">Admin</option>
              <option value="User">User</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 bg-white border-t border-gray-200 sticky bottom-0 z-20">
      <div class="flex justify-end mt-6 px-6 py-4">
        <div class="flex items-center space-x-4">
          <button
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            @click="validateAndContinue"
            :disabled="!allUsersMapped"
          >
            Next
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Toast Container -->
    <ToastContainer ref="toastContainer" />
  </div>
</template>

<script setup>
import { reactive, defineEmits, onMounted, computed , ref, nextTick } from 'vue'
import axios from 'axios'
import ToastContainer from '../ToastContainer.vue'
import { useToast } from '../../composables/useToast.js'

const localUsers = reactive([])
const isLoading = ref(true);
const originalAdminUsers = ref(new Set()); // Track users who were originally Admin

// Toast setup
const toastContainer = ref(null);
const toast = useToast(); // Only one useToast call


const emit = defineEmits(['next', 'back'])

// Computed properties for stats and validation
const mappedUsersCount = computed(() => {
  return localUsers.filter(user => user.status && user.status !== '').length
})

const allUsersMapped = computed(() => {
  return localUsers.length > 0 && localUsers.every(user => user.status && user.status !== '')
})

// Computed property to sort users with Admin roles first
const sortedUsers = computed(() => {
  return [...localUsers].sort((a, b) => {
    // Admin roles first
    if (a.status === 'Admin' && b.status !== 'Admin') return -1
    if (b.status === 'Admin' && a.status !== 'Admin') return 1
    // Then by name
    return a.name.localeCompare(b.name)
  })
})

const fetchUsers = async () => {
  isLoading.value = true;
  const portalId = new URLSearchParams(window.location.search).get('portal_id')

  if (!portalId) {
    error('Missing Portal ID', 'Portal ID is required in the URL to proceed.');
    isLoading.value = false;
    return
  }

  try {
    const { data } = await axios.get(`api/hubspot/users?portal_id=${portalId}`)

    if (data.ok && Array.isArray(data.data)) {
      // Track users who are originally Admin (loaded from database)
      originalAdminUsers.value.clear()
      data.data.forEach(user => {
        if (user.status === 'Admin') {
          originalAdminUsers.value.add(user.email)
        }
      })

      localUsers.splice(0, localUsers.length, ...data.data)
    } else {
      console.error('Unexpected response format:', data)
    }
  } catch (err) {
    console.error('Error fetching users:', err)
  } finally {
    isLoading.value = false;
  }

}

onMounted(async () => {
  // Initialize toast container after DOM is ready
  await nextTick();
  console.log('StepUserMapping mounted, toastContainer.value:', toastContainer.value);

  if (toastContainer.value) {
    toast.setToastContainer(toastContainer.value);
    console.log('Toast container initialized successfully');
  } else {
    console.error('Toast container not found!');
  }

  fetchUsers()
})

const validateAndContinue = async () => {
  console.log('validateAndContinue called, allUsersMapped:', allUsersMapped.value);

  if (!allUsersMapped.value) {
    console.log('Showing warning toast...');
    toast.warning('Incomplete Mapping', 'Please select a user status for all users before proceeding.');
    return
  }

  try {
    const portalId = new URLSearchParams(window.location.search).get('portal_id');

    await axios.post('api/hubspot/users/save', {
      users: localUsers,
      portal_id: portalId
    })

    console.log('Showing success toast...');
    toast.success('User Mappings Saved', `Successfully saved ${mappedUsersCount.value} user mapping(s). Proceeding to next step.`);

    setTimeout(() => {
      emit('next')
    }, 1500)
  } catch (error) {
    console.error('Error saving users:', error)
    console.log('Showing error toast...');
    toast.error('Save Failed', 'Failed to save user mappings. Please try again.');
  }
}

</script>
