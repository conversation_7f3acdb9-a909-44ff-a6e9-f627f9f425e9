<?php

namespace App\Hubspot\Crm;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
class ResponseBuilder
{
    private $delimiter;

    private $objectId;

    private $title;

    private $properties = [];

    private $results = [];

    private $primaryAction;

    /**
     * Constructor
     *
     * @param  string  $delimiter  The delimiter used for encoding
     */
    public function __construct($delimiter)
    {
        $this->delimiter = $delimiter;
    }

    /**
     * Reset the builder state
     *
     * @return $this
     */
    public function reset()
    {
        $this->title = null;
        $this->properties = [];
        $this->results = [];
        $this->primaryAction = null;
        // Do not reset objectId as it's set through setObjectId and persists

        return $this;
    }

    /**
     * Set the object ID
     *
     * @param  string  $objectId  Object ID
     * @return $this
     */
    public function setObjectId($objectId)
    {
        $this->objectId = $objectId;

        return $this;
    }

    /**
     * Set the title
     *
     * @param  string  $title  Title
     * @return $this
     */
    public function withTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Add a property
     *
     * @param  string  $label  Property label
     * @param  string  $value  Property value
     * @return $this
     */
    public function withProperty($label, $value)
    {
        $this->properties[] = [
            'label' => $label,
            'dataType' => 'STRING',
            'value' => $value,
        ];

        return $this;
    }

    /**
     * Create a new result builder
     *
     * @return ResultBuilder
     */
    public function addResult()
    {
        $resultBuilder = new ResultBuilder($this);
        // Set default objectId from parent if available
        if ($this->objectId) {
            $resultBuilder->forObject($this->objectId);
        }

        return $resultBuilder;
    }

    /**
     * Add a result to the results array
     *
     * @param  array  $result  Result data
     * @return $this
     */
    public function pushResult($result)
    {
        $this->results[] = $result;

        return $this;
    }

    /**
     * Add Instagram results to the response
     *
     * @param  object  $instagram  Instagram data
     * @return $this
     */
    public function addInstagramResults($instagram, $queryInput)
    {
        foreach ($instagram->accounts as $insta) {
            $this->addResult()
                ->withTitle('Dashboard Insta: '.$insta->instagram_username)
                ->withLink($this->buildUrl('/dashboard', array_merge(['username' => $insta->instagram_username], $queryInput), '#/insta'))
                ->add();
        }

        return $this;
    }

    /**
     * Add portal result to the response
     *
     * @param  object  $portal  Portal object
     * @param  array  $context  Context data including input, phone, userHasAccess, etc.
     * @return $this
     */
    public function addPortalResult($portal, array $context)
    {
        $queryInput = $this->prepareQueryInput($context['input'], $context['phone'], $portal);

        $resultBuilder = $this->addResult();
        //$cacheKey = 'portal:'.$context['input']['portalId'].':'.$context['input']['userEmail'];

        if (! $context['userHasAccess']) {
            $resultBuilder
                ->withTitle('Request Access for '.$context['input']['userEmail']);
                // ->withAction($this->createAccessRequestAction($context['input']['userEmail'], $queryInput['user_id'], $context['adminEmail']));

        } else {
          //  Cache::put($cacheKey, $queryInput['user_id'], now()->addHours(24));
            $resultBuilder
                ->withTitle("Setting")
                ->withLink($this->buildUrl('/wizard', $queryInput))
                ->withAction($this->createWhatsAppAction($queryInput));
        }

        $resultBuilder->add();

        return $this;
    }

    /**
     * Set the primary action
     *
     * @param  array  $action  Action data
     * @return $this
     */
    public function withPrimaryAction($action)
    {
        $this->primaryAction = $action;

        return $this;
    }

    /**
     * Prepare the query input for URL building
     *
     * @param  array  $input  Request input
     * @param  string  $phone  Parsed phone number
     * @param  object  $portal  Portal object
     * @return array Prepared query input
     */
    public function prepareQueryInput($input, $phone, $portal = null)
    {
        $encryptedUserId = urlencode(Crypt::encryptString($input['userId']));
        $queryInput = [
            'portal_id' => $input['portalId'],
            'email' => $input['userEmail'],
            'user_id' => $encryptedUserId //$input['userId'],
            // 'firstname' => $input['firstname'] ?? null,
            // 'lastname' => $input['lastname'] ?? null,
            // 'objectId' => $input['associatedObjectId'] ?? null,
            // 'phone' => $phone,
        ];

        // if ($portal && isset($portal->portal_id)) {
        //     $userId = $this->encodeUserId($portal);
        //     $queryInput['user_id'] = $userId;
        //     $queryInput['portal_id'] = $portal->portal_id;
        //     $queryInput['accountPhone'] = $portal->waba_phone;
        // }

        return $queryInput;
    }

    /**
     * Encode user ID
     *
     * @param  object  $portal  Portal object
     * @return string Encoded user ID
     */
    private function encodeUserId($portal)
    {
        $hashString = implode($this->delimiter, [
            $portal->id,
            $portal->portal_id,
            $portal->waba_phone,
        ]);

        return simple_crypt('e', $hashString);
    }

    /**
     * Create WhatsApp action
     *
     * @param  array  $queryInput  Query input
     * @return array Action configuration
     */
    private function createWhatsAppAction($queryInput)
    {
        return $this->createIframeAction('/initiate', $queryInput, 'See Mapping');
    }

    /**
     * Create access request action
     *
     * @param  string  $userEmail  User email
     * @param  string  $userId  User ID
     * @param  string  $adminUser  Admin user
     * @return array Action configuration
     */
    // private function createAccessRequestAction($userEmail, $userId, $adminUser)
    // {
    //     return $this->createIframeAction(
    //         "/request_access/{$userEmail}",
    //         ['user_id' => $userId],
    //         'Request Access for Vira. Admin: '.$adminUser
    //     );
    // }

    /**
     * Create a generic iframe action
     *
     * @param  string  $path  URL path
     * @param  array  $queryParams  Query parameters
     * @param  string  $label  Button label
     * @param  int  $width  Frame width
     * @param  int  $height  Frame height
     * @return array Action configuration
     */
    private function createIframeAction($path, $queryParams, $label, $width = 1280, $height = 900)
    {
        return [
            'type' => 'IFRAME',
            'width' => $width,
            'height' => $height,
            'uri' => $this->buildUrl($path, $queryParams),
            'label' => $label,
        ];
    }

    /**
     * Build URL with path, query, and fragment
     *
     * @param  string  $path  URL path
     * @param  array  $query  Query parameters
     * @param  string  $fragment  URL fragment
     * @return string Complete URL
     */
    private function buildUrl($path, $query = [], $fragment = '')
    {
        return env('APP_URL').$path.'?'.http_build_query($query).$fragment;
    }

    /**
     * Build the response
     *
     * @return array Response data
     */
    public function build()
    {
        $response = [];

        if (! empty($this->results)) {
            $response['results'] = $this->results;
        } elseif ($this->title) {
            $result = [
                'title' => $this->title,
                'properties' => $this->properties,
            ];

            // Only add objectId if it's set
            if ($this->objectId) {
                $result['objectId'] = $this->objectId;
            }

            $response['results'] = [$result];
        }

        if ($this->primaryAction) {
            $response['primaryAction'] = $this->primaryAction;
        }

        return $response;
    }
}
